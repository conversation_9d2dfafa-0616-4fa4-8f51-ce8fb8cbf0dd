import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style.css'
import 'virtual:uno.css'
import { HTTP } from '@funi/utils'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 配置全局HTTP请求对象
declare global {
  interface Window {
    $https: typeof HTTP
  }
}

window.$https = HTTP

createApp(App).use(router).use(ElementPlus).mount('#app')
