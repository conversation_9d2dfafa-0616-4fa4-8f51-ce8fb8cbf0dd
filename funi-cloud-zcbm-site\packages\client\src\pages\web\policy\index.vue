<!-- 政策法规页面 -->
<template>
  <BaseLayout>
    <div class="policy-page">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <h1 class="page-title">政策法规</h1>
          <p class="page-subtitle">了解最新政策法规，规范招标投标行为</p>
        </div>

        <!-- 分类导航 -->
        <div class="category-nav">
          <div class="nav-tabs">
            <div 
              v-for="category in categories" 
              :key="category.id"
              class="nav-tab"
              :class="{ active: activeCategory === category.id }"
              @click="setActiveCategory(category.id)"
            >
              <div class="tab-icon">
                <i :class="category.icon"></i>
              </div>
              <div class="tab-content">
                <h3>{{ category.name }}</h3>
                <p>{{ category.description }}</p>
                <span class="count">{{ category.count }}项</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-section">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="法规级别">
              <el-select 
                v-model="searchForm.level" 
                placeholder="请选择级别"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="国家级" value="national" />
                <el-option label="省级" value="provincial" />
                <el-option label="市级" value="municipal" />
                <el-option label="行业规范" value="industry" />
              </el-select>
            </el-form-item>
            <el-form-item label="关键词">
              <el-input 
                v-model="searchForm.keyword" 
                placeholder="请输入关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="发布时间">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <i class="i-mdi-magnify mr-1"></i>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <i class="i-mdi-refresh mr-1"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 政策法规列表 -->
        <div class="policy-list">
          <div class="list-header">
            <h3>{{ getCurrentCategoryName() }}</h3>
            <div class="sort-options">
              <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px">
                <el-option label="发布时间" value="publishDate" />
                <el-option label="实施时间" value="effectiveDate" />
                <el-option label="浏览量" value="viewCount" />
              </el-select>
            </div>
          </div>

          <div class="policy-items">
            <div 
              v-for="policy in filteredPolicies" 
              :key="policy.id"
              class="policy-item"
              @click="viewPolicyDetail(policy.id)"
            >
              <div class="policy-header">
                <div class="policy-title-wrapper">
                  <el-tag 
                    :type="getLevelTagType(policy.level)" 
                    size="small"
                    class="level-tag"
                  >
                    {{ getLevelText(policy.level) }}
                  </el-tag>
                  <h4 class="policy-title">{{ policy.title }}</h4>
                  <el-tag v-if="policy.isNew" type="success" size="small">新发布</el-tag>
                  <el-tag v-if="policy.isImportant" type="danger" size="small">重要</el-tag>
                </div>
                <div class="policy-dates">
                  <span class="publish-date">发布：{{ policy.publishDate }}</span>
                  <span class="effective-date">实施：{{ policy.effectiveDate }}</span>
                </div>
              </div>
              
              <div class="policy-summary">
                {{ policy.summary }}
              </div>
              
              <div class="policy-footer">
                <div class="policy-meta">
                  <span class="meta-item">
                    <i class="i-mdi-account"></i>
                    {{ policy.publisher }}
                  </span>
                  <span class="meta-item">
                    <i class="i-mdi-eye"></i>
                    {{ policy.viewCount }}
                  </span>
                  <span class="meta-item">
                    <i class="i-mdi-download"></i>
                    {{ policy.downloadCount }}
                  </span>
                </div>
                <div class="policy-actions">
                  <el-button type="primary" size="small">查看详情</el-button>
                  <el-button type="success" size="small" @click.stop="downloadPolicy(policy)">
                    <i class="i-mdi-download mr-1"></i>
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

const router = useRouter()

// 当前激活的分类
const activeCategory = ref('all')

// 排序方式
const sortBy = ref('publishDate')

// 搜索表单
const searchForm = ref({
  level: '',
  keyword: '',
  dateRange: null
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 分类数据
const categories = ref([
  {
    id: 'all',
    name: '全部法规',
    description: '所有政策法规',
    icon: 'i-mdi-file-document-multiple',
    count: 156
  },
  {
    id: 'bidding',
    name: '招标投标法',
    description: '招标投标相关法律法规',
    icon: 'i-mdi-gavel',
    count: 45
  },
  {
    id: 'procurement',
    name: '政府采购法',
    description: '政府采购相关法律法规',
    icon: 'i-mdi-bank',
    count: 38
  },
  {
    id: 'construction',
    name: '建设工程法规',
    description: '建设工程相关法律法规',
    icon: 'i-mdi-city',
    count: 42
  },
  {
    id: 'supervision',
    name: '监督管理办法',
    description: '监督管理相关规定',
    icon: 'i-mdi-shield-check',
    count: 31
  }
])

// 政策法规数据
const policyData = ref([
  {
    id: 1,
    title: '中华人民共和国招标投标法',
    summary: '为了规范招标投标活动，保护国家利益、社会公共利益和招标投标活动当事人的合法权益，提高经济效益，保证项目质量，制定本法。',
    category: 'bidding',
    level: 'national',
    publisher: '全国人大常委会',
    publishDate: '2017-12-28',
    effectiveDate: '2018-01-01',
    viewCount: 15678,
    downloadCount: 3456,
    isNew: false,
    isImportant: true
  },
  {
    id: 2,
    title: '政府采购法实施条例',
    summary: '为了规范政府采购行为，提高政府采购资金的使用效益，维护国家利益和社会公共利益，保护政府采购当事人的合法权益，促进廉政建设，制定本条例。',
    category: 'procurement',
    level: 'national',
    publisher: '国务院',
    publishDate: '2023-06-15',
    effectiveDate: '2023-07-01',
    viewCount: 8934,
    downloadCount: 2134,
    isNew: true,
    isImportant: true
  },
  {
    id: 3,
    title: '建设工程质量管理条例',
    summary: '为了加强对建设工程质量的管理，保证建设工程质量，保护人民生命和财产安全，制定本条例。',
    category: 'construction',
    level: 'national',
    publisher: '国务院',
    publishDate: '2023-03-20',
    effectiveDate: '2023-04-01',
    viewCount: 6789,
    downloadCount: 1567,
    isNew: true,
    isImportant: false
  }
])

// 过滤后的政策数据
const filteredPolicies = computed(() => {
  let filtered = policyData.value

  // 按分类过滤
  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(item => item.category === activeCategory.value)
  }

  // 按级别过滤
  if (searchForm.value.level) {
    filtered = filtered.filter(item => item.level === searchForm.value.level)
  }

  // 按关键词过滤
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    filtered = filtered.filter(item => 
      item.title.toLowerCase().includes(keyword) ||
      item.summary.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 设置激活分类
const setActiveCategory = (categoryId: string) => {
  activeCategory.value = categoryId
}

// 获取当前分类名称
const getCurrentCategoryName = () => {
  const category = categories.value.find(cat => cat.id === activeCategory.value)
  return category ? category.name : '全部法规'
}

// 获取级别文本
const getLevelText = (level: string) => {
  const levelMap = {
    national: '国家级',
    provincial: '省级',
    municipal: '市级',
    industry: '行业规范'
  }
  return levelMap[level] || level
}

// 获取级别标签类型
const getLevelTagType = (level: string) => {
  const typeMap = {
    national: 'danger',
    provincial: 'warning',
    municipal: 'primary',
    industry: 'info'
  }
  return typeMap[level] || 'info'
}

// 查看政策详情
const viewPolicyDetail = (id: number) => {
  router.push(`/policy/detail?id=${id}`)
}

// 下载政策
const downloadPolicy = (policy: any) => {
  console.log('下载政策:', policy.title)
  // 实际应该调用下载API
  // window.$https.downloadFile(`/api/policy/download/${policy.id}`, {})
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    level: '',
    keyword: '',
    dateRange: null
  }
  loadData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadData()
}

// 加载数据
const loadData = async () => {
  try {
    // 模拟API调用
    // const response = await window.$https.fetch('/api/policy/list', {
    //   category: activeCategory.value,
    //   ...searchForm.value,
    //   page: pagination.value.currentPage,
    //   pageSize: pagination.value.pageSize
    // })
    
    pagination.value.total = filteredPolicies.value.length
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.policy-page {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

// 分类导航
.category-nav {
  margin-bottom: 20px;

  .nav-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 15px;
  }

  .nav-tab {
    background: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.active {
      border-color: #3498db;
      background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
    }

    .tab-icon {
      width: 45px;
      height: 45px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: white;
      }
    }

    .tab-content {
      flex: 1;

      h3 {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0 0 4px 0;
      }

      .count {
        font-size: 11px;
        color: #3498db;
        font-weight: 500;
      }
    }
  }
}

// 搜索区域
.search-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-form {
    :deep(.el-form-item) {
      margin-bottom: 15px;
      margin-right: 20px;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #2c3e50;
    }
  }
}

// 政策列表
.policy-list {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;

    h3 {
      color: #2c3e50;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .policy-items {
    .policy-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 25px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 4px solid transparent;

      &:hover {
        background: #e9ecef;
        border-left-color: #3498db;
        transform: translateX(5px);
      }

      .policy-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;

        .policy-title-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;

          .level-tag {
            flex-shrink: 0;
          }

          .policy-title {
            flex: 1;
            color: #2c3e50;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.4;
          }
        }

        .policy-dates {
          display: flex;
          flex-direction: column;
          gap: 5px;
          text-align: right;
          flex-shrink: 0;

          .publish-date,
          .effective-date {
            color: #7f8c8d;
            font-size: 13px;
          }
        }
      }

      .policy-summary {
        color: #7f8c8d;
        line-height: 1.6;
        margin-bottom: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .policy-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .policy-meta {
          display: flex;
          gap: 20px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #95a5a6;
            font-size: 13px;

            i {
              font-size: 14px;
            }
          }
        }

        .policy-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 30px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li.is-active {
        background-color: #3498db;
        border-color: #3498db;
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #3498db;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .policy-page .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .category-nav .nav-tabs {
    grid-template-columns: 1fr;
  }

  .search-section {
    padding: 20px;

    .search-form {
      :deep(.el-form-item) {
        margin-right: 0;
        width: 100%;
      }

      :deep(.el-input),
      :deep(.el-select),
      :deep(.el-date-picker) {
        width: 100% !important;
      }
    }
  }

  .policy-list {
    padding: 20px;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .policy-item {
      padding: 20px;

      .policy-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .policy-dates {
          text-align: left;
        }
      }

      .policy-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .policy-meta {
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
