<!-- 咨询热线页面 -->
<template>
  <BaseLayout>
    <div class="hotline-page">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <h1 class="page-title">咨询热线</h1>
          <p class="page-subtitle">为您提供专业的招标采购咨询服务</p>
        </div>

        <!-- 联系方式卡片 -->
        <div class="contact-cards">
          <div class="cards-grid">
            <div class="contact-card">
              <div class="card-icon phone">
                <i class="i-mdi-phone"></i>
              </div>
              <div class="card-content">
                <h3>咨询热线</h3>
                <p class="contact-info">************</p>
                <p class="service-time">服务时间：周一至周五 9:00-18:00</p>
              </div>
            </div>
            
            <div class="contact-card">
              <div class="card-icon email">
                <i class="i-mdi-email"></i>
              </div>
              <div class="card-content">
                <h3>邮箱咨询</h3>
                <p class="contact-info"><EMAIL></p>
                <p class="service-time">24小时内回复</p>
              </div>
            </div>
            
            <div class="contact-card">
              <div class="card-icon location">
                <i class="i-mdi-map-marker"></i>
              </div>
              <div class="card-content">
                <h3>现场咨询</h3>
                <p class="contact-info">某某市某某区某某街道123号</p>
                <p class="service-time">周一至周五 9:00-17:00</p>
              </div>
            </div>
            
            <div class="contact-card">
              <div class="card-icon online">
                <i class="i-mdi-message-text"></i>
              </div>
              <div class="card-content">
                <h3>在线客服</h3>
                <p class="contact-info">点击开始对话</p>
                <p class="service-time">周一至周日 8:00-22:00</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="faq-section">
          <h2 class="section-title">常见问题</h2>
          <div class="faq-categories">
            <div 
              v-for="category in faqCategories" 
              :key="category.id"
              class="faq-category"
              :class="{ active: activeFaqCategory === category.id }"
              @click="setActiveFaqCategory(category.id)"
            >
              <i :class="category.icon"></i>
              <span>{{ category.name }}</span>
            </div>
          </div>
          
          <div class="faq-list">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item 
                v-for="faq in getCurrentFaqs()" 
                :key="faq.id"
                :title="faq.question"
                :name="faq.id"
              >
                <div class="faq-answer" v-html="faq.answer"></div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <!-- 在线咨询表单 -->
        <div class="consultation-form">
          <h2 class="section-title">在线咨询</h2>
          <div class="form-container">
            <el-form :model="consultationForm" :rules="formRules" ref="formRef" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="consultationForm.name" placeholder="请输入您的姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="consultationForm.phone" placeholder="请输入联系电话" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="consultationForm.email" placeholder="请输入邮箱地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="咨询类型" prop="type">
                    <el-select v-model="consultationForm.type" placeholder="请选择咨询类型" style="width: 100%">
                      <el-option label="招标流程咨询" value="process" />
                      <el-option label="政策法规咨询" value="policy" />
                      <el-option label="技术问题咨询" value="technical" />
                      <el-option label="其他问题" value="other" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="咨询内容" prop="content">
                <el-input 
                  v-model="consultationForm.content" 
                  type="textarea" 
                  :rows="5"
                  placeholder="请详细描述您的问题"
                />
              </el-form-item>
              
              <el-form-item label="附件">
                <el-upload
                  class="upload-demo"
                  action="/api/upload"
                  :file-list="consultationForm.files"
                  multiple
                  :limit="3"
                >
                  <el-button type="primary">上传文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持jpg/png/pdf/doc文件，最多3个文件，每个不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="submitConsultation" :loading="submitting">
                  提交咨询
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 服务承诺 -->
        <div class="service-promise">
          <h2 class="section-title">服务承诺</h2>
          <div class="promise-grid">
            <div class="promise-item">
              <div class="promise-icon">
                <i class="i-mdi-clock-fast"></i>
              </div>
              <h3>快速响应</h3>
              <p>电话咨询即时接听，在线咨询5分钟内回复</p>
            </div>
            
            <div class="promise-item">
              <div class="promise-icon">
                <i class="i-mdi-account-tie"></i>
              </div>
              <h3>专业服务</h3>
              <p>专业团队提供权威、准确的政策解读和业务指导</p>
            </div>
            
            <div class="promise-item">
              <div class="promise-icon">
                <i class="i-mdi-shield-check"></i>
              </div>
              <h3>信息保密</h3>
              <p>严格保护用户隐私，咨询信息绝对保密</p>
            </div>
            
            <div class="promise-item">
              <div class="promise-icon">
                <i class="i-mdi-thumb-up"></i>
              </div>
              <h3>满意服务</h3>
              <p>持续跟进服务质量，确保用户满意</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

// 当前激活的FAQ分类
const activeFaqCategory = ref('process')

// 当前展开的折叠面板
const activeCollapse = ref([])

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// FAQ分类
const faqCategories = ref([
  { id: 'process', name: '招标流程', icon: 'i-mdi-timeline' },
  { id: 'policy', name: '政策法规', icon: 'i-mdi-book-open' },
  { id: 'technical', name: '技术问题', icon: 'i-mdi-cog' },
  { id: 'other', name: '其他问题', icon: 'i-mdi-help-circle' }
])

// FAQ数据
const faqData = ref({
  process: [
    {
      id: 1,
      question: '如何参与招标投标？',
      answer: '<p>参与招标投标需要以下步骤：</p><ol><li>关注招标公告</li><li>购买招标文件</li><li>编制投标文件</li><li>递交投标文件</li><li>参加开标会议</li></ol>'
    },
    {
      id: 2,
      question: '投标保证金如何缴纳？',
      answer: '<p>投标保证金可通过以下方式缴纳：</p><ul><li>银行转账</li><li>银行保函</li><li>保险保函</li></ul><p>具体要求请参考招标文件。</p>'
    }
  ],
  policy: [
    {
      id: 3,
      question: '最新的招标投标法规有哪些？',
      answer: '<p>主要法规包括：</p><ul><li>《中华人民共和国招标投标法》</li><li>《招标投标法实施条例》</li><li>《政府采购法》</li></ul>'
    }
  ],
  technical: [
    {
      id: 4,
      question: '电子投标系统如何使用？',
      answer: '<p>使用电子投标系统的步骤：</p><ol><li>注册账号并实名认证</li><li>下载CA证书</li><li>制作电子投标文件</li><li>在线提交</li></ol>'
    }
  ],
  other: [
    {
      id: 5,
      question: '如何查询中标结果？',
      answer: '<p>可通过以下方式查询：</p><ul><li>官方网站公示栏</li><li>招标代理机构网站</li><li>电话咨询</li></ul>'
    }
  ]
})

// 咨询表单
const consultationForm = reactive({
  name: '',
  phone: '',
  email: '',
  type: '',
  content: '',
  files: []
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择咨询类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入咨询内容', trigger: 'blur' },
    { min: 10, message: '咨询内容至少10个字符', trigger: 'blur' }
  ]
}

// 设置激活的FAQ分类
const setActiveFaqCategory = (categoryId: string) => {
  activeFaqCategory.value = categoryId
  activeCollapse.value = []
}

// 获取当前分类的FAQ
const getCurrentFaqs = () => {
  return faqData.value[activeFaqCategory.value] || []
}

// 提交咨询
const submitConsultation = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 实际应该调用API
    // await window.$https.post('/api/consultation/submit', consultationForm)
    
    ElMessage.success('咨询提交成功，我们会尽快回复您！')
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(consultationForm, {
    name: '',
    phone: '',
    email: '',
    type: '',
    content: '',
    files: []
  })
}
</script>

<style lang="scss" scoped>
.hotline-page {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

// 联系方式卡片
.contact-cards {
  margin-bottom: 30px;

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .contact-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;

      i {
        font-size: 28px;
        color: white;
      }

      &.phone {
        background: linear-gradient(135deg, #27ae60, #229954);
      }

      &.email {
        background: linear-gradient(135deg, #3498db, #2980b9);
      }

      &.location {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
      }

      &.online {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
      }
    }

    .card-content {
      h3 {
        color: #2c3e50;
        margin: 0 0 10px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .contact-info {
        color: #3498db;
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      .service-time {
        color: #7f8c8d;
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

// 通用区块标题
.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 25px;
  text-align: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 2px;
  }
}

// 常见问题
.faq-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .faq-categories {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;

    .faq-category {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: #f8f9fa;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        background: #e9ecef;
      }

      &.active {
        background: #3498db;
        color: white;
        border-color: #2980b9;
      }

      i {
        font-size: 16px;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .faq-list {
    :deep(.el-collapse) {
      border: none;

      .el-collapse-item {
        margin-bottom: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;

        .el-collapse-item__header {
          background: #f8f9fa;
          padding: 15px 20px;
          font-weight: 500;
          color: #2c3e50;
          border: none;

          &:hover {
            background: #e9ecef;
          }
        }

        .el-collapse-item__content {
          padding: 20px;
          background: white;

          .faq-answer {
            line-height: 1.6;
            color: #2c3e50;

            p {
              margin-bottom: 10px;
            }

            ul, ol {
              margin: 10px 0;
              padding-left: 20px;

              li {
                margin-bottom: 5px;
              }
            }
          }
        }
      }
    }
  }
}

// 在线咨询表单
.consultation-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .form-container {
    max-width: 800px;
    margin: 0 auto;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #2c3e50;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
    }
  }
}

// 服务承诺
.service-promise {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .promise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
  }

  .promise-item {
    text-align: center;
    padding: 20px;

    .promise-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;

      i {
        font-size: 28px;
        color: white;
      }
    }

    h3 {
      color: #2c3e50;
      margin: 0 0 15px 0;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      color: #7f8c8d;
      line-height: 1.6;
      margin: 0;
    }
  }
}

@media (max-width: 768px) {
  .hotline-page .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .contact-cards .cards-grid {
    grid-template-columns: 1fr;
  }

  .faq-section {
    padding: 20px;

    .faq-categories {
      justify-content: flex-start;
      gap: 10px;

      .faq-category {
        padding: 10px 15px;
        font-size: 13px;
      }
    }
  }

  .consultation-form {
    padding: 20px;

    .form-container {
      :deep(.el-col) {
        margin-bottom: 15px;
      }
    }
  }

  .service-promise {
    padding: 20px;

    .promise-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
}
</style>
