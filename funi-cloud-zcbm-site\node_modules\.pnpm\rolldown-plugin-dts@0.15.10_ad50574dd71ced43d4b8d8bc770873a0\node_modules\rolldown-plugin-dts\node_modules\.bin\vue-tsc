#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules/vue-tsc/bin/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules/vue-tsc/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules/vue-tsc/bin/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules/vue-tsc/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/vue-tsc@3.0.6_typescript@5.9.2/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vue-tsc/bin/vue-tsc.js" "$@"
else
  exec node  "$basedir/../../../vue-tsc/bin/vue-tsc.js" "$@"
fi
