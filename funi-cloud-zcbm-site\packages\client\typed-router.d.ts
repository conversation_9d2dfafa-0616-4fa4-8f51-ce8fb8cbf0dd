/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/Main': RouteRecordInfo<'/Main', '/Main', Record<never, never>, Record<never, never>, '/Main/HomeDashboard' | '/Main/HomeDataStatistics' | '/Main/HomeProjectMonitoring'>,
    '/Main/HomeDashboard': RouteRecordInfo<'/Main/HomeDashboard', '/Main/HomeDashboard', Record<never, never>, Record<never, never>>,
    '/Main/HomeDataStatistics': RouteRecordInfo<'/Main/HomeDataStatistics', '/Main/HomeDataStatistics', Record<never, never>, Record<never, never>>,
    '/Main/HomeProjectMonitoring': RouteRecordInfo<'/Main/HomeProjectMonitoring', '/Main/HomeProjectMonitoring', Record<never, never>, Record<never, never>>,
    '/Operation/AnnouncementDetails': RouteRecordInfo<'/Operation/AnnouncementDetails', '/Operation/AnnouncementDetails', Record<never, never>, Record<never, never>>,
    '/Operation/AnnouncementManagement': RouteRecordInfo<'/Operation/AnnouncementManagement', '/Operation/AnnouncementManagement', Record<never, never>, Record<never, never>>,
    '/Operation/AnnouncementReview': RouteRecordInfo<'/Operation/AnnouncementReview', '/Operation/AnnouncementReview', Record<never, never>, Record<never, never>>,
    '/Operation/MyTodo': RouteRecordInfo<'/Operation/MyTodo', '/Operation/MyTodo', Record<never, never>, Record<never, never>>,
    '/Operation/ProjectSupervision': RouteRecordInfo<'/Operation/ProjectSupervision', '/Operation/ProjectSupervision', Record<never, never>, Record<never, never>>,
    '/Operation/TenderDetails': RouteRecordInfo<'/Operation/TenderDetails', '/Operation/TenderDetails', Record<never, never>, Record<never, never>>,
    '/Operation/TenderDetailsReview': RouteRecordInfo<'/Operation/TenderDetailsReview', '/Operation/TenderDetailsReview', Record<never, never>, Record<never, never>>,
    '/Operation/TenderDetailsReview2': RouteRecordInfo<'/Operation/TenderDetailsReview2', '/Operation/TenderDetailsReview2', Record<never, never>, Record<never, never>>,
  }

  /**
   * Route file to route info map by unplugin-vue-router.
   * Used by the volar plugin to automatically type useRoute()
   *
   * Each key is a file path relative to the project root with 2 properties:
   * - routes: union of route names of the possible routes when in this page (passed to useRoute<...>())
   * - views: names of nested views (can be passed to <RouterView name="...">)
   *
   * @internal
   */
  export interface _RouteFileInfoMap {
    'src/pages/h5/Main.vue': {
      routes: '/Main' | '/Main/HomeDashboard' | '/Main/HomeDataStatistics' | '/Main/HomeProjectMonitoring'
      views: 'default'
    }
    'src/pages/h5/Main/HomeDashboard.vue': {
      routes: '/Main/HomeDashboard'
      views: never
    }
    'src/pages/h5/Main/HomeDataStatistics.vue': {
      routes: '/Main/HomeDataStatistics'
      views: never
    }
    'src/pages/h5/Main/HomeProjectMonitoring.vue': {
      routes: '/Main/HomeProjectMonitoring'
      views: never
    }
    'src/pages/h5/Operation/AnnouncementDetails.vue': {
      routes: '/Operation/AnnouncementDetails'
      views: never
    }
    'src/pages/h5/Operation/AnnouncementManagement.vue': {
      routes: '/Operation/AnnouncementManagement'
      views: never
    }
    'src/pages/h5/Operation/AnnouncementReview.vue': {
      routes: '/Operation/AnnouncementReview'
      views: never
    }
    'src/pages/h5/Operation/MyTodo.vue': {
      routes: '/Operation/MyTodo'
      views: never
    }
    'src/pages/h5/Operation/ProjectSupervision.vue': {
      routes: '/Operation/ProjectSupervision'
      views: never
    }
    'src/pages/h5/Operation/TenderDetails.vue': {
      routes: '/Operation/TenderDetails'
      views: never
    }
    'src/pages/h5/Operation/TenderDetailsReview.vue': {
      routes: '/Operation/TenderDetailsReview'
      views: never
    }
    'src/pages/h5/Operation/TenderDetailsReview2.vue': {
      routes: '/Operation/TenderDetailsReview2'
      views: never
    }
  }

  /**
   * Get a union of possible route names in a certain route component file.
   * Used by the volar plugin to automatically type useRoute()
   *
   * @internal
   */
  export type _RouteNamesForFilePath<FilePath extends string> =
    _RouteFileInfoMap extends Record<FilePath, infer Info>
      ? Info['routes']
      : keyof RouteNamedMap
}
