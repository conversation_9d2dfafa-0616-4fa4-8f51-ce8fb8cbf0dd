/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/hotline/': RouteRecordInfo<'/hotline/', '/hotline', Record<never, never>, Record<never, never>>,
    '/integrity/': RouteRecordInfo<'/integrity/', '/integrity', Record<never, never>, Record<never, never>>,
    '/Main/HomeDashboard': RouteRecordInfo<'/Main/HomeDashboard', '/Main/HomeDashboard', Record<never, never>, Record<never, never>>,
    '/notice/': RouteRecordInfo<'/notice/', '/notice', Record<never, never>, Record<never, never>>,
    '/policy/': RouteRecordInfo<'/policy/', '/policy', Record<never, never>, Record<never, never>>,
    '/trade/': RouteRecordInfo<'/trade/', '/trade', Record<never, never>, Record<never, never>>,
    'TradeDetail': RouteRecordInfo<'TradeDetail', '/trade/detail', Record<never, never>, Record<never, never>>,
  }

  /**
   * Route file to route info map by unplugin-vue-router.
   * Used by the volar plugin to automatically type useRoute()
   *
   * Each key is a file path relative to the project root with 2 properties:
   * - routes: union of route names of the possible routes when in this page (passed to useRoute<...>())
   * - views: names of nested views (can be passed to <RouterView name="...">)
   *
   * @internal
   */
  export interface _RouteFileInfoMap {
    'src/pages/web/hotline/index.vue': {
      routes: '/hotline/'
      views: never
    }
    'src/pages/web/integrity/index.vue': {
      routes: '/integrity/'
      views: never
    }
    'src/pages/web/Main/HomeDashboard.vue': {
      routes: '/Main/HomeDashboard'
      views: never
    }
    'src/pages/web/notice/index.vue': {
      routes: '/notice/'
      views: never
    }
    'src/pages/web/policy/index.vue': {
      routes: '/policy/'
      views: never
    }
    'src/pages/web/trade/index.vue': {
      routes: '/trade/'
      views: never
    }
    'src/pages/web/trade/detail.vue': {
      routes: 'TradeDetail'
      views: never
    }
  }

  /**
   * Get a union of possible route names in a certain route component file.
   * Used by the volar plugin to automatically type useRoute()
   *
   * @internal
   */
  export type _RouteNamesForFilePath<FilePath extends string> =
    _RouteFileInfoMap extends Record<FilePath, infer Info>
      ? Info['routes']
      : keyof RouteNamedMap
}
