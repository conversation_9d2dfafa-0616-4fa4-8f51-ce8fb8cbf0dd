# Web开发框架总结

## 项目概述

本项目是一个基于 Vue 3 + Vite + TypeScript 的现代化前端项目，采用 Monorepo 架构，支持 Web 和 H5 双端开发。

## 技术栈

### 核心框架
- **Vue 3.5.18** - 前端框架
- **Vite** (rolldown-vite@^7.1.0) - 构建工具
- **TypeScript** - 类型系统
- **Vue Router 4.5.1** - 路由管理

### 构建工具
- **pnpm** - 包管理器
- **Rolldown** - 基于 Rust 的高性能打包器
- **unplugin-vue-router** - 自动路由生成

### UI 框架
- **Element Plus 2.11.1** - UI 组件库
- **UnoCSS** - 原子化 CSS 框架
- **@iconify** - 图标库 (Carbon + MDI)

### 开发工具
- **ESLint** (@antfu/eslint-config) - 代码规范
- **Vue TSC** - TypeScript 类型检查
- **simple-git-hooks** - Git 钩子

## 项目结构

```
funi-cloud-zcbm-site/
├── packages/
│   ├── client/          # 前端应用
│   │   ├── src/
│   │   │   ├── pages/
│   │   │   │   ├── web/     # Web端页面
│   │   │   │   └── h5/      # H5端页面
│   │   │   ├── components/  # 组件
│   │   │   ├── router/      # 路由配置
│   │   │   └── assets/      # 静态资源
│   │   ├── theme/           # 主题配置
│   │   └── vite.config.ts   # Vite配置
│   └── utils/               # 工具库
│       ├── http/            # HTTP请求封装
│       └── tools/           # 通用工具
├── docs/                    # 文档
└── pnpm-workspace.yaml      # Monorepo配置
```

## 开发配置

### 路径别名
```typescript
// vite.config.ts
alias: {
  '@': path.resolve('./src'),
  '@h5': path.resolve('./src/pages/h5'),
  '@web': path.resolve('./src/pages/web')
}
```

### 环境变量
- `VITE_RUN_MODE`: 运行模式 (web/h5)

### 启动命令
```bash
# Web端开发
pnpm dev:web

# H5端开发  
pnpm dev:h5

# 构建
pnpm build:web
pnpm build:h5
```

## HTTP 请求规范

### 使用 window.$https

项目使用自定义的 HTTP 封装，通过 `window.$https` 全局对象进行请求：

```typescript
// 基于 @funi-lib/utils 的 BaseApi 类
import { HTTP } from '@funi/utils'

// 使用方式
window.$https = HTTP
```

### 请求特性

1. **自动加密解密**
   - 支持网关加密 (`/gateway/` 路径)
   - 支持 isgateway 加密 (`/isgateway/` 路径)
   - 自动查询加密列表并匹配

2. **请求拦截**
   - 自动添加 Authorization token
   - 支持请求取消 (AbortController)

3. **响应拦截**
   - 自动解密响应数据
   - 统一错误处理
   - Blob 文件处理

### 请求方法

```typescript
// GET 请求
await window.$https.fetch(url, params)

// POST 请求
await window.$https.post(url, data)

// 文件下载
await window.$https.downloadFile(url, params)

// 文件上传
await window.$https.upload2(url, formData)
```

## 样式规范

### UnoCSS 配置
- 使用 Wind3 预设 (类似 Tailwind CSS)
- 支持属性化模式
- 内置图标支持
- 自定义主题色彩

### 主题系统
- 支持动态主题切换
- CSS 变量管理
- 预设色彩方案

### 常用样式类
```css
/* 边框 */
.border-base        /* 基础边框 */
.border-base-light  /* 浅色边框 */

/* 背景 */
.bg-base           /* 基础背景 */
.bg-base-light     /* 浅色背景 */

/* 文字 */
.text-base         /* 基础文字色 */
.text-base-regular /* 常规文字色 */
.text-base-secondry /* 次要文字色 */
.text-base-disable  /* 禁用文字色 */
```

## 路由配置

### 自动路由生成
使用 `unplugin-vue-router` 基于文件系统自动生成路由：

```typescript
// 路由文件夹根据运行模式动态切换
routesFolder: `src/pages/${VITE_RUN_MODE}`

// 排除规则
exclude: [
  '**/components/**/*',  // 组件文件夹
  '**/_*.vue',          // 下划线开头的文件
  '**/icon_*.vue',      // 图标组件
]
```

### 路由配置
```typescript
// 默认重定向
{ path: '/', redirect: '/Main/HomeDashboard' }

// 使用 Hash 模式
history: createWebHashHistory()
```

## 开发注意事项

### 1. 代码规范
- 使用 ESLint + @antfu/eslint-config
- 提交前自动格式化 (lint-staged)
- 遵循 Vue 3 Composition API 规范

### 2. 类型安全
- 严格的 TypeScript 配置
- 组件 props 类型定义
- API 响应类型定义

### 3. 性能优化
- 使用 Rolldown 高性能打包
- 组件懒加载
- 图标按需加载

### 4. 请求处理
- **必须使用** `window.$https` 进行 HTTP 请求
- 不需要手动处理加密解密
- 统一的错误处理机制
- 自动 token 管理

### 5. 组件开发
- 优先使用 Element Plus 组件
- 自定义组件放在对应端的 components 目录
- 使用 UnoCSS 进行样式开发

### 6. 文件组织
- 页面文件放在 `pages/web/` 目录
- 组件文件放在 `components/web/` 目录
- 工具函数放在 `utils/` 包中
- 静态资源放在 `assets/` 目录

### 7. 环境配置
- 开发环境使用 `pnpm dev:web`
- 构建时会自动进行类型检查
- 支持热更新和快速重载

## 依赖管理

使用 pnpm workspace + catalog 模式统一管理依赖版本，确保各包版本一致性。

### Catalog 分类
- `build`: 构建相关依赖
- `deps`: 业务依赖  
- `devtools`: 开发工具
- `frontend`: 前端框架

## 总结

本项目采用现代化的前端开发技术栈，具有良好的开发体验和性能表现。开发时需要：

1. 严格遵循项目规范和目录结构
2. 使用 `window.$https` 进行所有 HTTP 请求
3. 充分利用 TypeScript 类型系统
4. 遵循 Vue 3 最佳实践
5. 合理使用 UnoCSS 和 Element Plus
