<!-- 首页 -->
<template>
  <main class="main-content">
    <!-- 快捷入口 -->
    <section>
      <groupTitle title="快捷入口" :icon="quickAccess"></groupTitle>
      <div class="flex gap-20px">
        <router-link v-for="item in quickAccessItems" :key="item.name" :to="item.path"
          class="w-225px h-172px p-20px border-solid border-[#ededed] border-1 rounded-8px no-underline">
          <img :src="item.icon" class="mb-12px w-48px h-48px">
          <div class="text-20px leading-28px color-black mb-4px">{{ item.name }}</div>
          <div class="text-size-14px color-[#999999]">{{ item.description }}</div>
        </router-link>
      </div>
    </section>

    <!-- 招标公告 -->
    <section class="mt-40px">
      <groupTitle title="招标公示" :icon="zhaobiao" desc="查看更多招标公告"></groupTitle>
      <el-tabs v-model="activeNoticeTab">
        <el-tab-pane v-for="tab in noticeTabs" :key="tab.value" :label="tab.label" :name="tab.value">
          <div>
            <div v-for="notice in getNoticesByType(tab.value)" :key="notice.id"
              class="flex justify-between b-b border-[#ededed] h-77px items-center">
              <div class="text-14px">
                <div class="text-16px m-b-10px">成都淮州新城建设投资有限公司淮州建投2025年临时服务单位选标公告</div>
                <div class="color-green">公开招标</div>
              </div>
              <span class="notice-date">{{ notice.date }}</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </section>

    <!-- 结果公示 -->
    <section class="mt-40px">
      <groupTitle title="结果公示" :icon="jieguo" desc="查看更多结果公示"></groupTitle>
      <el-tabs v-model="activeResultTab">
        <el-tab-pane v-for="tab in resultTabs" :key="tab.value" :label="tab.label" :name="tab.value">
          <div class="flex flex-wrap gap-20px">
            <div v-for="result in getResultsByType(tab.value)" :key="result.id" class="border-[#ededed] b-1 p-20px w-470px rounded-8px">
              <div class="text-16px">{{ result.title }}</div>
              <div class="flex justify-between text-14px mt-10px">
                <div class="color-[#999999]">标段类型：</div>
                <div>{{ result.date }}</div>
              </div>
              <div class="flex justify-between text-14px mt-10px">
                <div class="color-[#999999]">项目状态：</div>
                <div>{{ result.date }}</div>
              </div>
              <div class="flex justify-between text-14px mt-10px">
                <div class="color-[#999999]">发布时间：</div>
                <div>{{ result.date }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </section>

    <!-- 友情链接 -->
    <section class="mt-40px mb-77px">
      <groupTitle title="友情链接" :icon="youqing" desc="查看更多结果公示"></groupTitle>
        <el-tabs v-model="activeLinkTab">
          <el-tab-pane v-for="tab in linkTabs" :key="tab.value" :label="tab.label" :name="tab.value">
            <div class="flex flex-wrap gap-20px mt-5px">
              <a v-for="link in getLinksByType(tab.value)" :key="link.name" :href="link.url" target="_blank"
                class="w-470px leading-60px bg-[#F7F7F7] text-align-center no-underline color-black">
                {{ link.name }}
              </a>
            </div>
          </el-tab-pane>
        </el-tabs>
    </section>
  </main>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import groupTitle from '@/components/web/groupTitle/index.vue'

import quickAccess from '@/assets/web/quick-access.png'
import zhaobiao from '@/assets/web/zhaobiao.png'
import jieguo from '@/assets/web/jieguo.png'
import youqing from '@/assets/web/youqing.png'
import trade from '@/assets/web/trade.png'
import integrity from '@/assets/web/integrity.png'
import notice from '@/assets/web/notice.png'
import policy from '@/assets/web/policy.png'


// 快捷入口数据
const quickAccessItems = ref([
  {
    name: '交易信息',
    path: '/trade',
    icon: trade,
    description: '查看最新的采购项目信息、招标公告、中标结果等'
  },
  {
    name: '诚信监督',
    path: '/integrity',
    icon: integrity,
    description: '供应商诚信记录查询、黑名单公示、信用评级'
  },
  {
    name: '通知公告',
    path: '/notice',
    icon: notice,
    description: '平台重要通知、系统公告、操作指南'
  },
  {
    name: '政策法规',
    path: '/policy',
    icon: policy,
    description: '政府采购相关法律法规、政策文件、操作规范'
  }
])

// 招标公告相关
const activeNoticeTab = ref('all')
const noticeTabs = ref([
  { label: '全部', value: 'all' },
  { label: '工程', value: 'construction' },
  { label: '物资', value: 'material' },
  { label: '服务', value: 'service' },
  { label: '其他', value: 'other' }
])

// 模拟招标公告数据
const noticeData = ref([
  { id: 1, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: true, isUrgent: false },
  { id: 2, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: false, isUrgent: true },
  { id: 3, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: true, isUrgent: false },
  { id: 4, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: false, isUrgent: false },
  { id: 5, title: '成都市政府办公设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'material', isNew: true, isUrgent: false },
  { id: 6, title: '成都市政府办公设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'material', isNew: false, isUrgent: true },
  { id: 7, title: '成都市政府咨询服务采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'service', isNew: true, isUrgent: false },
  { id: 8, title: '成都市政府咨询服务采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'service', isNew: false, isUrgent: false },
  { id: 9, title: '成都市其他类型采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'other', isNew: false, isUrgent: true },
  { id: 10, title: '成都市其他类型采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'other', isNew: true, isUrgent: false }
])

// 结果公示相关
const activeResultTab = ref('all')
const resultTabs = ref([
  { label: '全部', value: 'all' },
  { label: '工程', value: 'construction' },
  { label: '物资', value: 'material' },
  { label: '服务', value: 'service' },
  { label: '其他', value: 'other' }
])

// 模拟结果公示数据
const resultData = ref([
  {
    id: 1,
    title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购结果公告',
    description: '经过公开招标，确定中标单位为某某建设有限公司',
    date: '2025-07-08',
    winner: '某某建设有限公司',
    status: 'winning',
    statusText: '公开招标',
    type: 'construction'
  },
  {
    id: 2,
    title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购结果公告',
    description: '政府办公设备采购项目已完成，成交供应商为某某科技公司',
    date: '2025-07-08',
    winner: '某某科技公司',
    status: 'transaction',
    statusText: '竞争性磋商',
    type: 'material'
  },
  {
    id: 3,
    title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购结果公告',
    description: '服务采购项目已完成，成交供应商为某某服务公司',
    date: '2025-07-08',
    winner: '某某服务公司',
    status: 'winning',
    statusText: '公开招标',
    type: 'service'
  },
  {
    id: 4,
    title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购结果公告',
    description: '其他类型采购项目已完成',
    date: '2025-07-08',
    winner: '某某公司',
    status: 'transaction',
    statusText: '竞争性磋商',
    type: 'other'
  }
])

// 友情链接相关
const activeLinkTab = ref('national')
const linkTabs = ref([
  { label: '国家级', value: 'national' },
  { label: '省级', value: 'provincial' },
  { label: '市级', value: 'municipal' }
])

// 友情链接数据
const friendlyLinks = ref({
  national: [
    { name: '信用中国', url: 'http://www.creditchina.gov.cn' },
    { name: '中国政府采购网', url: 'http://www.ccgp.gov.cn' },
    { name: '全国公共资源交易平台', url: 'http://www.ggzy.gov.cn' },
    { name: '中国招标投标网', url: 'http://www.cebpubservice.com' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '财政部', url: 'http://www.mof.gov.cn' }
  ],
  provincial: [
    { name: '中国政府网政务服务平台', url: 'http://www.gov.cn' },
    { name: '全国公共资源交易平台', url: 'http://www.ggzy.gov.cn' },
    { name: '国际时事部', url: 'http://www.example.com' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '国家住房和城乡建设部', url: 'http://www.mohurd.gov.cn' },
    { name: '中华人民共和国财政部', url: 'http://www.mof.gov.cn' }
  ],
  municipal: [
    { name: '中国网政府采购网', url: 'http://www.example.com' },
    { name: '中国招标投标网', url: 'http://www.cebpubservice.com' },
    { name: '国家住房和城乡建设部', url: 'http://www.mohurd.gov.cn' },
    { name: '中华人民共和国财政部', url: 'http://www.mof.gov.cn' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '中华人民共和国人民政府', url: 'http://www.gov.cn' }
  ]
})

// 获取指定类型的招标公告
const getNoticesByType = (type: string) => {
  if (type === 'all') {
    return noticeData.value.slice(0, 4)
  }
  return noticeData.value.filter(item => item.type === type).slice(0, 4)
}

// 获取指定类型的结果公示
const getResultsByType = (type: string) => {
  if (type === 'all') {
    return resultData.value.slice(0, 4)
  }
  return resultData.value.filter(item => item.type === type).slice(0, 4)
}

// 获取指定类型的友情链接
const getLinksByType = (type: string) => {
  return friendlyLinks.value[type as keyof typeof friendlyLinks.value] || []
}
</script>

<style lang="scss" scoped>
.home-dashboard {
  background: #f5f7fa;
  min-height: calc(100vh - 200px);

  .dashboard-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 20px;
    padding: 20px;
  }
}

// 左侧边栏
.sidebar {
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;

  .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;

    .header-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1890ff;
      font-weight: 600;

      &.active {
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .sidebar-nav {
    padding: 10px 0;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      color: #666;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f9ff;
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }
}

// 右侧主要内容
.main-content {
  flex: 1;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
      margin: 0;

      i {
        font-size: 20px;
      }
    }

    .more-link {
      color: #1890ff;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}


// 招标公告样式
.tender-notice {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .notice-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .notice-list {
    .notice-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .notice-content {
        flex: 1;

        .notice-title {
          color: #333;
          text-decoration: none;
          font-size: 14px;
          line-height: 1.5;
          display: block;
          margin-bottom: 8px;

          &:hover {
            color: #1890ff;
          }
        }

        .notice-tags {
          display: flex;
          gap: 8px;
        }
      }

      .notice-date {
        color: #999;
        font-size: 12px;
        margin-left: 15px;
        flex-shrink: 0;
      }
    }
  }
}

// 结果公示样式
.result-announcement {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .result-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .result-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .result-card {
    background: #fafafa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f9ff;
      border-color: #1890ff;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;

      .card-title {
        flex: 1;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin: 0;
      }

      .card-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        margin-left: 8px;

        &.winning {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.transaction {
          background: #e6f7ff;
          color: #1890ff;
          border: 1px solid #91d5ff;
        }

        &.failed {
          background: #fff2e8;
          color: #fa8c16;
          border: 1px solid #ffd591;
        }
      }
    }

    .card-content {
      .card-description {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .card-meta {
        display: flex;
        justify-content: space-between;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #999;
          font-size: 11px;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .card-footer {
      margin-top: 10px;
      text-align: right;

      .view-detail {
        display: inline-block;
        padding: 4px 12px;
        background: #1890ff;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 12px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }
}

// 友情链接样式
.friendly-links {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .links-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .links-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .link-item {
    display: block;
    padding: 8px 12px;
    background: #fafafa;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #1890ff;
      color: white;
      border-color: #1890ff;
    }
  }
}
</style>