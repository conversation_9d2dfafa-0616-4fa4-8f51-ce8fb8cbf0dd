<!-- 首页 -->
<template>
  <BaseLayout>
    <div class="home-dashboard">
      <div class="dashboard-container">
        <!-- 左侧导航菜单 -->
        <aside class="sidebar">
          <div class="sidebar-header">
            <div class="header-item active">
              <i class="i-mdi-home"></i>
              <span>首页</span>
            </div>
          </div>
          <nav class="sidebar-nav">
            <router-link
              v-for="item in sidebarItems"
              :key="item.name"
              :to="item.path"
              class="nav-item"
            >
              <i :class="item.icon"></i>
              <span>{{ item.name }}</span>
            </router-link>
          </nav>
        </aside>

        <!-- 右侧主要内容区域 -->
        <main class="main-content">
          <!-- 快捷入口 -->
          <section class="quick-access">
            <div class="section-header">
              <h2 class="section-title">
                <i class="i-mdi-lightning-bolt"></i>
                快捷入口
              </h2>
            </div>
            <div class="access-grid">
              <router-link
                v-for="item in quickAccessItems"
                :key="item.name"
                :to="item.path"
                class="access-item"
              >
                <div class="access-icon" :style="{ backgroundColor: item.color }">
                  <i :class="item.icon"></i>
                </div>
                <div class="access-content">
                  <span class="access-name">{{ item.name }}</span>
                  <span class="access-desc">{{ item.description }}</span>
                </div>
              </router-link>
            </div>
          </section>

          <!-- 招标公告 -->
          <section class="tender-notice">
            <div class="section-header">
              <h2 class="section-title">
                <i class="i-mdi-file-document"></i>
                招标公告
              </h2>
              <router-link to="/notice" class="more-link">
                查看更多招标公告 >
              </router-link>
            </div>
            <el-tabs v-model="activeNoticeTab" class="notice-tabs">
              <el-tab-pane
                v-for="tab in noticeTabs"
                :key="tab.value"
                :label="tab.label"
                :name="tab.value"
              >
                <div class="notice-list">
                  <div
                    v-for="notice in getNoticesByType(tab.value)"
                    :key="notice.id"
                    class="notice-item"
                  >
                    <div class="notice-content">
                      <router-link :to="`/trade/detail?id=${notice.id}`" class="notice-title">
                        {{ notice.title }}
                      </router-link>
                      <div class="notice-tags">
                        <el-tag v-if="notice.isNew" type="success" size="small">分开招标</el-tag>
                        <el-tag v-if="notice.isUrgent" type="warning" size="small">资格预审</el-tag>
                      </div>
                    </div>
                    <span class="notice-date">{{ notice.date }}</span>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </section>

          <!-- 结果公示 -->
          <section class="result-announcement">
            <div class="section-header">
              <h2 class="section-title">
                <i class="i-mdi-clipboard-check"></i>
                结果公示
              </h2>
              <router-link to="/notice" class="more-link">
                查看更多结果公示 >
              </router-link>
            </div>
            <el-tabs v-model="activeResultTab" class="result-tabs">
            <el-tab-pane
              v-for="tab in resultTabs"
              :key="tab.value"
              :label="tab.label"
              :name="tab.value"
            >
              <div class="result-cards">
                <div
                  v-for="result in getResultsByType(tab.value)"
                  :key="result.id"
                  class="result-card"
                >
                  <div class="card-header">
                    <h3 class="card-title">{{ result.title }}</h3>
                    <span class="card-status" :class="result.status">{{ result.statusText }}</span>
                  </div>
                  <div class="card-content">
                    <p class="card-description">{{ result.description }}</p>
                    <div class="card-meta">
                      <span class="meta-item">
                        <i class="i-mdi-calendar"></i>
                        {{ result.date }}
                      </span>
                      <span class="meta-item">
                        <i class="i-mdi-account"></i>
                        {{ result.winner }}
                      </span>
                    </div>
                  </div>
                  <div class="card-footer">
                    <router-link :to="`/trade/detail?id=${result.id}`" class="view-detail">
                      查看详情
                    </router-link>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          </section>

          <!-- 友情链接 -->
          <section class="friendly-links">
            <div class="section-header">
              <h2 class="section-title">
                <i class="i-mdi-link"></i>
                友情链接
              </h2>
            </div>
            <div class="links-tabs">
              <el-tabs v-model="activeLinkTab" class="link-tabs">
                <el-tab-pane
                  v-for="tab in linkTabs"
                  :key="tab.value"
                  :label="tab.label"
                  :name="tab.value"
                >
                  <div class="links-grid">
                    <a
                      v-for="link in getLinksByType(tab.value)"
                      :key="link.name"
                      :href="link.url"
                      target="_blank"
                      class="link-item"
                    >
                      {{ link.name }}
                    </a>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </section>
        </main>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

// 侧边栏导航数据
const sidebarItems = ref([
  { name: '交易信息', path: '/trade', icon: 'i-mdi-file-document-outline' },
  { name: '诚信监督', path: '/integrity', icon: 'i-mdi-shield-check-outline' },
  { name: '通知公告', path: '/notice', icon: 'i-mdi-bullhorn-outline' },
  { name: '政策法规', path: '/policy', icon: 'i-mdi-book-open-outline' },
  { name: '咨询热线', path: '/hotline', icon: 'i-mdi-phone' }
])

// 快捷入口数据
const quickAccessItems = ref([
  {
    name: '交易信息',
    path: '/trade',
    icon: 'i-mdi-file-document-outline',
    color: '#52c41a',
    description: '招标投标信息查询，投标文件下载'
  },
  {
    name: '诚信监督',
    path: '/integrity',
    icon: 'i-mdi-shield-check-outline',
    color: '#1890ff',
    description: '企业信用查询，黑名单查询'
  },
  {
    name: '通知公告',
    path: '/notice',
    icon: 'i-mdi-bullhorn-outline',
    color: '#fa8c16',
    description: '政策通知，系统公告'
  },
  {
    name: '政策法规',
    path: '/policy',
    icon: 'i-mdi-book-open-outline',
    color: '#722ed1',
    description: '招标投标相关法律法规'
  }
])

// 招标公告相关
const activeNoticeTab = ref('all')
const noticeTabs = ref([
  { label: '全部公告', value: 'all' },
  { label: '工程建设', value: 'construction' },
  { label: '政府采购', value: 'government' },
  { label: '产权交易', value: 'property' }
])

// 模拟招标公告数据
const noticeData = ref([
  { id: 1, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: true, isUrgent: false },
  { id: 2, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'government', isNew: false, isUrgent: true },
  { id: 3, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'property', isNew: true, isUrgent: false },
  { id: 4, title: '成都市市政道路建设项目设备采购2025年度招标服务政府采购公告', date: '2025-07-08', type: 'construction', isNew: false, isUrgent: false }
])

// 结果公示相关
const activeResultTab = ref('all')
const resultTabs = ref([
  { label: '全部结果', value: 'all' },
  { label: '中标公示', value: 'winning' },
  { label: '成交公告', value: 'transaction' },
  { label: '流标公告', value: 'failed' }
])

// 模拟结果公示数据
const resultData = ref([
  {
    id: 1,
    title: '某某工程项目中标结果',
    description: '经过公开招标，确定中标单位为某某建设有限公司',
    date: '2024-01-10',
    winner: '某某建设有限公司',
    status: 'winning',
    statusText: '中标公示',
    type: 'winning'
  },
  {
    id: 2,
    title: '办公设备采购成交公告',
    description: '政府办公设备采购项目已完成，成交供应商为某某科技公司',
    date: '2024-01-09',
    winner: '某某科技公司',
    status: 'transaction',
    statusText: '成交公告',
    type: 'transaction'
  }
])

// 友情链接相关
const activeLinkTab = ref('national')
const linkTabs = ref([
  { label: '国家级', value: 'national' },
  { label: '省级', value: 'provincial' },
  { label: '市级', value: 'municipal' }
])

// 友情链接数据
const friendlyLinks = ref({
  national: [
    { name: '信用中国', url: 'http://www.creditchina.gov.cn' },
    { name: '中国政府采购网', url: 'http://www.ccgp.gov.cn' },
    { name: '全国公共资源交易平台', url: 'http://www.ggzy.gov.cn' },
    { name: '中国招标投标网', url: 'http://www.cebpubservice.com' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '财政部', url: 'http://www.mof.gov.cn' }
  ],
  provincial: [
    { name: '中国政府网政务服务平台', url: 'http://www.gov.cn' },
    { name: '全国公共资源交易平台', url: 'http://www.ggzy.gov.cn' },
    { name: '国际时事部', url: 'http://www.example.com' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '国家住房和城乡建设部', url: 'http://www.mohurd.gov.cn' },
    { name: '中华人民共和国财政部', url: 'http://www.mof.gov.cn' }
  ],
  municipal: [
    { name: '中国网政府采购网', url: 'http://www.example.com' },
    { name: '中国招标投标网', url: 'http://www.cebpubservice.com' },
    { name: '国家住房和城乡建设部', url: 'http://www.mohurd.gov.cn' },
    { name: '中华人民共和国财政部', url: 'http://www.mof.gov.cn' },
    { name: '国家发展改革委', url: 'http://www.ndrc.gov.cn' },
    { name: '中华人民共和国人民政府', url: 'http://www.gov.cn' }
  ]
})

// 获取指定类型的招标公告
const getNoticesByType = (type: string) => {
  if (type === 'all') {
    return noticeData.value.slice(0, 4)
  }
  return noticeData.value.filter(item => item.type === type).slice(0, 4)
}

// 获取指定类型的结果公示
const getResultsByType = (type: string) => {
  if (type === 'all') {
    return resultData.value.slice(0, 4)
  }
  return resultData.value.filter(item => item.type === type).slice(0, 4)
}

// 获取指定类型的友情链接
const getLinksByType = (type: string) => {
  return friendlyLinks.value[type as keyof typeof friendlyLinks.value] || []
}
</script>

<style lang="scss" scoped>
.home-dashboard {
  background: #f5f7fa;
  min-height: calc(100vh - 200px);

  .dashboard-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 20px;
    padding: 20px;
  }
}

// 左侧边栏
.sidebar {
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;

  .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;

    .header-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1890ff;
      font-weight: 600;

      &.active {
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .sidebar-nav {
    padding: 10px 0;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      color: #666;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f9ff;
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }
}

// 右侧主要内容
.main-content {
  flex: 1;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
      margin: 0;

      i {
        font-size: 20px;
      }
    }

    .more-link {
      color: #1890ff;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 快捷入口样式
.quick-access {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .access-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .access-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #fafafa;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #f0f9ff;
      border-color: #1890ff;
    }

    .access-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: white;
      }
    }

    .access-content {
      flex: 1;

      .access-name {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        display: block;
      }

      .access-desc {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }
  }
}

// 招标公告样式
.tender-notice {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .notice-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .notice-list {
    .notice-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .notice-content {
        flex: 1;

        .notice-title {
          color: #333;
          text-decoration: none;
          font-size: 14px;
          line-height: 1.5;
          display: block;
          margin-bottom: 8px;

          &:hover {
            color: #1890ff;
          }
        }

        .notice-tags {
          display: flex;
          gap: 8px;
        }
      }

      .notice-date {
        color: #999;
        font-size: 12px;
        margin-left: 15px;
        flex-shrink: 0;
      }
    }
  }
}

// 结果公示样式
.result-announcement {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .result-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .result-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .result-card {
    background: #fafafa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f9ff;
      border-color: #1890ff;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;

      .card-title {
        flex: 1;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        margin: 0;
      }

      .card-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        margin-left: 8px;

        &.winning {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.transaction {
          background: #e6f7ff;
          color: #1890ff;
          border: 1px solid #91d5ff;
        }

        &.failed {
          background: #fff2e8;
          color: #fa8c16;
          border: 1px solid #ffd591;
        }
      }
    }

    .card-content {
      .card-description {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .card-meta {
        display: flex;
        justify-content: space-between;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #999;
          font-size: 11px;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .card-footer {
      margin-top: 10px;
      text-align: right;

      .view-detail {
        display: inline-block;
        padding: 4px 12px;
        background: #1890ff;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 12px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #40a9ff;
        }
      }
    }
  }
}

// 友情链接样式
.friendly-links {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .links-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #f0f0f0;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      padding: 0 16px;

      &.is-active {
        color: #1890ff;
      }
    }
  }

  .links-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .link-item {
    display: block;
    padding: 8px 12px;
    background: #fafafa;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #1890ff;
      color: white;
      border-color: #1890ff;
    }
  }
}

@media (max-width: 768px) {
  .home-dashboard {
    .dashboard-container {
      flex-direction: column;
      padding: 15px;
    }
  }

  .sidebar {
    width: 100%;
    margin-bottom: 15px;

    .sidebar-nav {
      display: flex;
      overflow-x: auto;
      padding: 10px;

      .nav-item {
        white-space: nowrap;
        margin-right: 10px;
      }
    }
  }

  .main-content {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }

  .quick-access .access-grid {
    grid-template-columns: 1fr;
  }

  .result-cards {
    grid-template-columns: 1fr;
  }

  .friendly-links .links-grid {
    grid-template-columns: 1fr;
  }
}
</style>