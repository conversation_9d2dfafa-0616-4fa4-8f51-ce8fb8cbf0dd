<!-- 诚信监督页面 -->
<template>
  <BaseLayout>
    <div class="integrity-page">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <h1 class="page-title">诚信监督</h1>
          <p class="page-subtitle">建设诚信体系，维护市场秩序</p>
        </div>

        <!-- 功能导航 -->
        <div class="function-nav">
          <div class="nav-grid">
            <div 
              v-for="item in navItems" 
              :key="item.id"
              class="nav-item"
              @click="setActiveTab(item.id)"
              :class="{ active: activeTab === item.id }"
            >
              <div class="nav-icon">
                <i :class="item.icon"></i>
              </div>
              <div class="nav-content">
                <h3>{{ item.title }}</h3>
                <p>{{ item.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
          <!-- 黑名单查询 -->
          <div v-if="activeTab === 'blacklist'" class="tab-content">
            <div class="search-bar">
              <el-input
                v-model="searchKeyword"
                placeholder="请输入企业名称或统一社会信用代码"
                class="search-input"
              >
                <template #append>
                  <el-button type="primary" @click="handleSearch">
                    <i class="i-mdi-magnify"></i>
                  </el-button>
                </template>
              </el-input>
            </div>
            
            <div class="blacklist-results">
              <el-table :data="blacklistData" stripe>
                <el-table-column prop="companyName" label="企业名称" min-width="200" />
                <el-table-column prop="creditCode" label="统一社会信用代码" width="200" />
                <el-table-column prop="reason" label="列入原因" min-width="250" />
                <el-table-column prop="listDate" label="列入时间" width="120" />
                <el-table-column prop="period" label="有效期" width="120" />
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="danger">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 信用评价 -->
          <div v-if="activeTab === 'credit'" class="tab-content">
            <div class="credit-stats">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon aaa">
                    <i class="i-mdi-star"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ creditStats.aaa }}</div>
                    <div class="stat-label">AAA级企业</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon aa">
                    <i class="i-mdi-star-half-full"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ creditStats.aa }}</div>
                    <div class="stat-label">AA级企业</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon a">
                    <i class="i-mdi-star-outline"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ creditStats.a }}</div>
                    <div class="stat-label">A级企业</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon total">
                    <i class="i-mdi-chart-pie"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ creditStats.total }}</div>
                    <div class="stat-label">总评价企业</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="credit-list">
              <el-table :data="creditData" stripe>
                <el-table-column prop="companyName" label="企业名称" min-width="200" />
                <el-table-column prop="creditCode" label="统一社会信用代码" width="200" />
                <el-table-column label="信用等级" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getCreditTagType(row.creditLevel)">
                      {{ row.creditLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="score" label="信用分数" width="100" />
                <el-table-column prop="evaluateDate" label="评价时间" width="120" />
                <el-table-column prop="validDate" label="有效期至" width="120" />
              </el-table>
            </div>
          </div>

          <!-- 投诉举报 -->
          <div v-if="activeTab === 'complaint'" class="tab-content">
            <div class="complaint-form">
              <h3>投诉举报</h3>
              <el-form :model="complaintForm" label-width="120px">
                <el-form-item label="举报类型">
                  <el-select v-model="complaintForm.type" placeholder="请选择举报类型">
                    <el-option label="围标串标" value="collusion" />
                    <el-option label="虚假投标" value="fake" />
                    <el-option label="违法分包" value="subcontract" />
                    <el-option label="其他违法行为" value="other" />
                  </el-select>
                </el-form-item>
                <el-form-item label="被举报企业">
                  <el-input v-model="complaintForm.company" placeholder="请输入企业名称" />
                </el-form-item>
                <el-form-item label="项目名称">
                  <el-input v-model="complaintForm.project" placeholder="请输入项目名称" />
                </el-form-item>
                <el-form-item label="举报内容">
                  <el-input 
                    v-model="complaintForm.content" 
                    type="textarea" 
                    :rows="5"
                    placeholder="请详细描述举报内容"
                  />
                </el-form-item>
                <el-form-item label="联系方式">
                  <el-input v-model="complaintForm.contact" placeholder="请输入联系电话或邮箱" />
                </el-form-item>
                <el-form-item label="证据材料">
                  <el-upload
                    class="upload-demo"
                    action="/api/upload"
                    :file-list="complaintForm.files"
                    multiple
                  >
                    <el-button type="primary">上传文件</el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持jpg/png/pdf文件，且不超过10MB
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitComplaint">提交举报</el-button>
                  <el-button @click="resetComplaintForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 监督公示 -->
          <div v-if="activeTab === 'supervision'" class="tab-content">
            <div class="supervision-list">
              <div class="list-header">
                <h3>监督公示</h3>
                <el-button type="primary" size="small">
                  <i class="i-mdi-plus mr-1"></i>
                  发布公示
                </el-button>
              </div>
              
              <div class="supervision-items">
                <div 
                  v-for="item in supervisionData" 
                  :key="item.id"
                  class="supervision-item"
                >
                  <div class="item-header">
                    <h4>{{ item.title }}</h4>
                    <el-tag :type="getSupervisionTagType(item.type)">
                      {{ getSupervisionTypeText(item.type) }}
                    </el-tag>
                  </div>
                  <div class="item-content">
                    <p>{{ item.content }}</p>
                  </div>
                  <div class="item-footer">
                    <span class="publish-date">发布时间：{{ item.publishDate }}</span>
                    <span class="view-count">浏览量：{{ item.viewCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

// 当前激活的标签
const activeTab = ref('blacklist')

// 搜索关键词
const searchKeyword = ref('')

// 导航项目
const navItems = ref([
  {
    id: 'blacklist',
    title: '黑名单查询',
    description: '查询失信企业名单',
    icon: 'i-mdi-account-remove'
  },
  {
    id: 'credit',
    title: '信用评价',
    description: '企业信用等级查询',
    icon: 'i-mdi-star-circle'
  },
  {
    id: 'complaint',
    title: '投诉举报',
    description: '违法违规行为举报',
    icon: 'i-mdi-alert-circle'
  },
  {
    id: 'supervision',
    title: '监督公示',
    description: '监督检查结果公示',
    icon: 'i-mdi-eye'
  }
])

// 黑名单数据
const blacklistData = ref([
  {
    id: 1,
    companyName: '某某建设有限公司',
    creditCode: '91110000123456789X',
    reason: '围标串标',
    listDate: '2024-01-10',
    period: '2年',
    status: '有效'
  }
])

// 信用统计
const creditStats = ref({
  aaa: 156,
  aa: 234,
  a: 189,
  total: 579
})

// 信用评价数据
const creditData = ref([
  {
    id: 1,
    companyName: '优秀建设有限公司',
    creditCode: '91110000987654321A',
    creditLevel: 'AAA',
    score: 95,
    evaluateDate: '2024-01-01',
    validDate: '2024-12-31'
  }
])

// 投诉举报表单
const complaintForm = ref({
  type: '',
  company: '',
  project: '',
  content: '',
  contact: '',
  files: []
})

// 监督公示数据
const supervisionData = ref([
  {
    id: 1,
    title: '关于某某项目违规行为的处理公示',
    type: 'penalty',
    content: '经调查核实，某某企业在某某项目中存在违规行为，现予以公示...',
    publishDate: '2024-01-15',
    viewCount: 1256
  }
])

// 设置激活标签
const setActiveTab = (tab: string) => {
  activeTab.value = tab
}

// 搜索处理
const handleSearch = () => {
  console.log('搜索:', searchKeyword.value)
  // 实际应该调用API
}

// 获取信用等级标签类型
const getCreditTagType = (level: string) => {
  const typeMap = {
    'AAA': 'success',
    'AA': 'primary',
    'A': 'warning'
  }
  return typeMap[level] || 'info'
}

// 获取监督类型文本
const getSupervisionTypeText = (type: string) => {
  const typeMap = {
    penalty: '处罚公示',
    warning: '警告公示',
    notice: '通知公示'
  }
  return typeMap[type] || type
}

// 获取监督类型标签
const getSupervisionTagType = (type: string) => {
  const typeMap = {
    penalty: 'danger',
    warning: 'warning',
    notice: 'info'
  }
  return typeMap[type] || 'info'
}

// 提交投诉
const submitComplaint = () => {
  console.log('提交投诉:', complaintForm.value)
  // 实际应该调用API
}

// 重置投诉表单
const resetComplaintForm = () => {
  complaintForm.value = {
    type: '',
    company: '',
    project: '',
    content: '',
    contact: '',
    files: []
  }
}

onMounted(() => {
  // 加载数据
})
</script>

<style lang="scss" scoped>
.integrity-page {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

// 功能导航
.function-nav {
  margin-bottom: 20px;

  .nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .nav-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.active {
      border-color: #3498db;
      background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
    }

    .nav-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }
    }

    .nav-content {
      flex: 1;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 5px 0;
      }

      p {
        font-size: 14px;
        color: #7f8c8d;
        margin: 0;
      }
    }
  }
}

// 内容区域
.content-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .tab-content {
    .search-bar {
      margin-bottom: 25px;

      .search-input {
        max-width: 500px;

        :deep(.el-input-group__append) {
          background-color: #3498db;
          border-color: #3498db;

          .el-button {
            background: transparent;
            border: none;
            color: white;
          }
        }
      }
    }

    // 信用统计
    .credit-stats {
      margin-bottom: 30px;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        border-left: 4px solid transparent;

        .stat-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;

          i {
            font-size: 24px;
            color: white;
          }

          &.aaa {
            background: linear-gradient(135deg, #27ae60, #229954);
          }

          &.aa {
            background: linear-gradient(135deg, #3498db, #2980b9);
          }

          &.a {
            background: linear-gradient(135deg, #f39c12, #e67e22);
          }

          &.total {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
          }
        }

        .stat-info {
          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1;
          }

          .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
          }
        }
      }
    }

    // 投诉举报表单
    .complaint-form {
      h3 {
        color: #2c3e50;
        margin-bottom: 25px;
        font-size: 20px;
        font-weight: 600;
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #2c3e50;
      }
    }

    // 监督公示
    .supervision-list {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;

        h3 {
          color: #2c3e50;
          margin: 0;
          font-size: 20px;
          font-weight: 600;
        }
      }

      .supervision-items {
        .supervision-item {
          background: #f8f9fa;
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 15px;
          border-left: 4px solid #3498db;

          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;

            h4 {
              flex: 1;
              color: #2c3e50;
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              line-height: 1.4;
            }
          }

          .item-content {
            margin-bottom: 15px;

            p {
              color: #7f8c8d;
              line-height: 1.6;
              margin: 0;
            }
          }

          .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #95a5a6;
          }
        }
      }
    }
  }
}

// 表格样式
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;
  }

  .el-table th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
  }

  .el-table td {
    border-bottom: 1px solid #f0f0f0;
  }
}

@media (max-width: 768px) {
  .integrity-page .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .function-nav .nav-grid {
    grid-template-columns: 1fr;
  }

  .content-section {
    padding: 20px;
  }

  .credit-stats .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .supervision-list .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>
