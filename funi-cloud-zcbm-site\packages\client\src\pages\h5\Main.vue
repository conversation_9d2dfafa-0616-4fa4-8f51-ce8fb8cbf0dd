<script setup lang="tsx">
import icon_dashboard from '@h5/Main/icons/icon_dashboard.vue'
import icon_dataStatistics from '@h5/Main/icons/icon_dataStatistics.vue'
import icon_projectMonitoring from '@h5/Main/icons/icon_projectMonitoring.vue'
import { computed, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()

const homeMenus = reactive([
  {
    name: '工作台',
    icon: icon_dashboard,
    key: 'dashboard',
    path: '/Main/HomeDashboard',
  },
  {
    name: '项目监督',
    icon: icon_projectMonitoring,
    key: 'projectMonitoring',
    path: '/Main/HomeProjectMonitoring',
  },
  {
    name: '数据统计',
    icon: icon_dataStatistics,
    key: 'dataStatistics',
    path: '/Main/HomeDataStatistics',
  },
])
const actived = computed(() => {
  const { path } = route
  return homeMenus.find(e => e.path === path)?.key ?? 'dashboard'
})

function menuClick(menu: { key: string, path: string }) {
  router.push({ path: menu.path })
}
</script>

<template>
  <div pd-4 h-full w-full overflow-hidden flex="~ col justify-between items-center">
    <div flex-1 overflow-auto bg-gray-1 w-full>
      <router-view />
    </div>
    <div flex="~  justify-between align-center" w-full relative z-10 shadow-top>
      <div v-for="menu in homeMenus" :key="menu.name" w-full text-center py-3 flex="~ col justify-between items-center" :class="actived === menu.key ? 'text-color-primary' : 'text-base'" @click="menuClick(menu)">
        <component :is="menu.icon" mb-1 />
        <div text-xs>
          {{ menu.name }}
        </div>
      </div>
    </div>
  </div>
</template>
