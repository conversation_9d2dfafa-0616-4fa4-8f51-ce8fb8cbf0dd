<!-- 通知公告页面 -->
<template>
  <BaseLayout>
    <div class="notice-page">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <h1 class="page-title">通知公告</h1>
          <p class="page-subtitle">及时了解最新政策动态和重要通知</p>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="公告类型">
              <el-select 
                v-model="searchForm.type" 
                placeholder="请选择公告类型"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="政策通知" value="policy" />
                <el-option label="系统公告" value="system" />
                <el-option label="业务通知" value="business" />
                <el-option label="紧急通知" value="urgent" />
              </el-select>
            </el-form-item>
            <el-form-item label="关键词">
              <el-input 
                v-model="searchForm.keyword" 
                placeholder="请输入关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="发布时间">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <i class="i-mdi-magnify mr-1"></i>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <i class="i-mdi-refresh mr-1"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon policy">
                <i class="i-mdi-file-document"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.policy }}</div>
                <div class="stat-label">政策通知</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon system">
                <i class="i-mdi-cog"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.system }}</div>
                <div class="stat-label">系统公告</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon business">
                <i class="i-mdi-briefcase"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.business }}</div>
                <div class="stat-label">业务通知</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon urgent">
                <i class="i-mdi-alert"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.urgent }}</div>
                <div class="stat-label">紧急通知</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 公告列表 -->
        <div class="notice-list">
          <div class="list-header">
            <h3>公告列表</h3>
            <div class="view-mode">
              <el-button-group>
                <el-button 
                  :type="viewMode === 'list' ? 'primary' : ''" 
                  @click="setViewMode('list')"
                >
                  <i class="i-mdi-view-list"></i>
                  列表
                </el-button>
                <el-button 
                  :type="viewMode === 'card' ? 'primary' : ''" 
                  @click="setViewMode('card')"
                >
                  <i class="i-mdi-view-grid"></i>
                  卡片
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'" class="list-view">
            <div 
              v-for="notice in noticeData" 
              :key="notice.id"
              class="notice-item"
              @click="viewNoticeDetail(notice.id)"
            >
              <div class="notice-header">
                <div class="notice-title-wrapper">
                  <el-tag 
                    :type="getNoticeTagType(notice.type)" 
                    size="small"
                    class="notice-type-tag"
                  >
                    {{ getNoticeTypeText(notice.type) }}
                  </el-tag>
                  <h4 class="notice-title">{{ notice.title }}</h4>
                  <el-tag v-if="notice.isTop" type="danger" size="small">置顶</el-tag>
                  <el-tag v-if="notice.isUrgent" type="warning" size="small">紧急</el-tag>
                </div>
                <span class="notice-date">{{ notice.publishDate }}</span>
              </div>
              <div class="notice-summary">
                {{ notice.summary }}
              </div>
              <div class="notice-footer">
                <div class="notice-meta">
                  <span class="meta-item">
                    <i class="i-mdi-account"></i>
                    {{ notice.publisher }}
                  </span>
                  <span class="meta-item">
                    <i class="i-mdi-eye"></i>
                    {{ notice.viewCount }}
                  </span>
                </div>
                <el-button type="primary" size="small">查看详情</el-button>
              </div>
            </div>
          </div>

          <!-- 卡片视图 -->
          <div v-if="viewMode === 'card'" class="card-view">
            <div class="cards-grid">
              <div 
                v-for="notice in noticeData" 
                :key="notice.id"
                class="notice-card"
                @click="viewNoticeDetail(notice.id)"
              >
                <div class="card-header">
                  <el-tag 
                    :type="getNoticeTagType(notice.type)" 
                    size="small"
                  >
                    {{ getNoticeTypeText(notice.type) }}
                  </el-tag>
                  <div class="card-badges">
                    <el-tag v-if="notice.isTop" type="danger" size="small">置顶</el-tag>
                    <el-tag v-if="notice.isUrgent" type="warning" size="small">紧急</el-tag>
                  </div>
                </div>
                <div class="card-content">
                  <h4 class="card-title">{{ notice.title }}</h4>
                  <p class="card-summary">{{ notice.summary }}</p>
                </div>
                <div class="card-footer">
                  <div class="card-meta">
                    <span class="meta-item">
                      <i class="i-mdi-calendar"></i>
                      {{ notice.publishDate }}
                    </span>
                    <span class="meta-item">
                      <i class="i-mdi-eye"></i>
                      {{ notice.viewCount }}
                    </span>
                  </div>
                  <span class="publisher">{{ notice.publisher }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

const router = useRouter()

// 搜索表单
const searchForm = ref({
  type: '',
  keyword: '',
  dateRange: null
})

// 视图模式
const viewMode = ref('list')

// 统计数据
const stats = ref({
  policy: 45,
  system: 23,
  business: 67,
  urgent: 8
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 公告数据
const noticeData = ref([
  {
    id: 1,
    title: '关于优化招标投标流程的通知',
    summary: '为进一步提高招标投标效率，简化办事流程，现对相关流程进行优化调整...',
    type: 'policy',
    publisher: '市公共资源交易中心',
    publishDate: '2024-01-15',
    viewCount: 1256,
    isTop: true,
    isUrgent: false
  },
  {
    id: 2,
    title: '系统维护公告',
    summary: '因系统升级需要，平台将于本周六进行维护，维护期间暂停服务...',
    type: 'system',
    publisher: '技术部',
    publishDate: '2024-01-14',
    viewCount: 892,
    isTop: false,
    isUrgent: true
  },
  {
    id: 3,
    title: '关于加强投标保证金管理的通知',
    summary: '为规范投标保证金管理，保护投标人合法权益，现就有关事项通知如下...',
    type: 'business',
    publisher: '业务管理部',
    publishDate: '2024-01-13',
    viewCount: 567,
    isTop: false,
    isUrgent: false
  }
])

// 获取公告类型文本
const getNoticeTypeText = (type: string) => {
  const typeMap = {
    policy: '政策通知',
    system: '系统公告',
    business: '业务通知',
    urgent: '紧急通知'
  }
  return typeMap[type] || type
}

// 获取公告类型标签
const getNoticeTagType = (type: string) => {
  const typeMap = {
    policy: 'primary',
    system: 'info',
    business: 'success',
    urgent: 'danger'
  }
  return typeMap[type] || 'info'
}

// 设置视图模式
const setViewMode = (mode: string) => {
  viewMode.value = mode
}

// 查看公告详情
const viewNoticeDetail = (id: number) => {
  router.push(`/notice/detail?id=${id}`)
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    type: '',
    keyword: '',
    dateRange: null
  }
  loadData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadData()
}

// 加载数据
const loadData = async () => {
  try {
    // 模拟API调用
    // const response = await window.$https.fetch('/api/notice/list', {
    //   ...searchForm.value,
    //   page: pagination.value.currentPage,
    //   pageSize: pagination.value.pageSize
    // })
    
    pagination.value.total = noticeData.value.length
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.notice-page {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

// 搜索区域
.search-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-form {
    :deep(.el-form-item) {
      margin-bottom: 15px;
      margin-right: 20px;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #2c3e50;
    }
  }
}

// 统计区域
.stats-section {
  margin-bottom: 20px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .stat-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-3px);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }

      &.policy {
        background: linear-gradient(135deg, #3498db, #2980b9);
      }

      &.system {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
      }

      &.business {
        background: linear-gradient(135deg, #27ae60, #229954);
      }

      &.urgent {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
      }
    }

    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: #7f8c8d;
        margin-top: 5px;
      }
    }
  }
}

// 公告列表
.notice-list {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;

    h3 {
      color: #2c3e50;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }

    .view-mode {
      :deep(.el-button-group .el-button) {
        padding: 8px 12px;
      }
    }
  }

  // 列表视图
  .list-view {
    .notice-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 4px solid transparent;

      &:hover {
        background: #e9ecef;
        border-left-color: #3498db;
        transform: translateX(5px);
      }

      .notice-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .notice-title-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;

          .notice-type-tag {
            flex-shrink: 0;
          }

          .notice-title {
            flex: 1;
            color: #2c3e50;
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
          }
        }

        .notice-date {
          color: #7f8c8d;
          font-size: 14px;
          flex-shrink: 0;
        }
      }

      .notice-summary {
        color: #7f8c8d;
        line-height: 1.6;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .notice-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .notice-meta {
          display: flex;
          gap: 20px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #95a5a6;
            font-size: 13px;

            i {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // 卡片视图
  .card-view {
    .cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }

    .notice-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e9ecef;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #3498db;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .card-badges {
          display: flex;
          gap: 5px;
        }
      }

      .card-content {
        margin-bottom: 15px;

        .card-title {
          color: #2c3e50;
          margin: 0 0 10px 0;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .card-summary {
          color: #7f8c8d;
          line-height: 1.6;
          margin: 0;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-meta {
          display: flex;
          flex-direction: column;
          gap: 5px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #95a5a6;
            font-size: 12px;

            i {
              font-size: 13px;
            }
          }
        }

        .publisher {
          color: #7f8c8d;
          font-size: 12px;
          text-align: right;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 30px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li.is-active {
        background-color: #3498db;
        border-color: #3498db;
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #3498db;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .notice-page .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .search-section {
    padding: 20px;

    .search-form {
      :deep(.el-form-item) {
        margin-right: 0;
        width: 100%;
      }

      :deep(.el-input),
      :deep(.el-select),
      :deep(.el-date-picker) {
        width: 100% !important;
      }
    }
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .notice-list {
    padding: 20px;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .notice-item .notice-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .cards-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
