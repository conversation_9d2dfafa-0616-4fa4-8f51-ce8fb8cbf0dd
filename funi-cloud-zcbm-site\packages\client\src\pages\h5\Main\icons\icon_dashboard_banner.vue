<script setup lang="ts">
defineProps<{
  time?: number | string
}>()
const image = new URL('@/assets/home/<USER>', import.meta.url).href
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 375 120" fill="none">
    <rect x="0" y="0" width="375" height="120" fill="#FFFFFF" />
    <g mask="url(#mask-5_5)">
      <path d="M0 0L375 0L375 140L0 140L0 0Z" fill="url(#linear_fill_5_7)" />
      <g mask="url(#mask-5_7)">
        <g filter="url(#filter_5_8)">
          <path d="M418 21.5C418 84.1843 367.184 135 304.5 135C241.816 135 191 84.1843 191 21.5C191 -41.1843 241.816 -92 304.5 -92C367.184 -92 418 -41.1843 418 21.5Z" fill="#B5F5FF" />
        </g>
      </g>
      <g mask="url(#mask-5_7)">
        <g filter="url(#filter_5_9)">
          <path d="M144 39.8761C144 61.9675 111.765 79.8761 72 79.8761C32.2355 79.8761 0 61.9675 0 39.8761C0 17.7847 32.2355 -0.123901 72 -0.123901C111.765 -0.123901 144 17.7847 144 39.8761Z" fill="#AFFAF4" />
        </g>
      </g>
      <g mask="url(#mask-5_7)" opacity="0.5">
        <g filter="url(#filter_5_10)">
          <path d="M144 126.876C144 148.967 111.765 166.876 72 166.876C32.2355 166.876 0 148.967 0 126.876C0 104.785 32.2355 86.8761 72 86.8761C111.765 86.8761 144 104.785 144 126.876Z" fill="#FAAFEF" />
        </g>
      </g>
      <g mask="url(#mask-5_7)">
        <g filter="url(#filter_5_11)">
          <path d="M300 39.8761C300 61.9675 267.764 79.8761 228 79.8761C188.236 79.8761 156 61.9675 156 39.8761C156 17.7847 188.236 -0.123901 228 -0.123901C267.764 -0.123901 300 17.7847 300 39.8761Z" fill="#FFCEF8" />
        </g>
      </g>
      <g mask="url(#mask-5_7)">
        <g filter="url(#filter_5_12)">
          <path d="M411 94.8761C411 116.967 378.764 134.876 339 134.876C299.236 134.876 267 116.967 267 94.8761C267 72.7847 299.236 54.8761 339 54.8761C378.764 54.8761 411 72.7847 411 94.8761Z" fill="#B6C5FF" />
        </g>
      </g>
    </g>
    <g mask="url(#mask-5_4)" opacity="0.3">
      <g filter="url(#filter_5_53)">
        <rect x="68.5" y="0" width="133" height="133" fill="url(#pattern_fill_5_53)" />
      </g>
    </g>
    <g mask="url(#mask-5_4)">
      <path fill-rule="evenodd" fill="url(#linear_fill_5_55_0)" d="M115.504 50.62C116.2 49.5806 116.847 48.5253 117.454 47.45L119.224 50.62L123.004 50.62L119.274 43.9C120.131 42.072 120.866 40.2884 121.464 38.55C122.062 36.8116 122.486 35.3003 122.754 34.01L112.954 34.01L112.954 30.09C116.703 30.0004 119.808 29.7805 122.254 29.44L122.254 26C120.004 26.233 117.754 26.4025 115.514 26.51C113.273 26.6175 111.298 26.6821 109.584 26.7L109.584 38.53C109.584 40.8956 109.401 42.9658 109.044 44.74C108.687 46.5142 108.093 48.4695 107.254 50.62L111.004 50.62C111.647 48.7562 112.12 46.9987 112.424 45.35C112.727 43.7013 112.898 41.9034 112.934 39.95L115.284 44.47C114.016 46.7998 112.737 48.8458 111.434 50.62L115.504 50.62ZM71.4507 45.4075C72.5754 44.834 73.6167 44.2206 74.5807 43.5575L74.5807 50.5475L94.3207 50.5475L94.3207 37.6375L80.5307 37.6375C80.9056 37.0819 81.105 36.7692 81.1407 36.6975L95.8507 36.6975L95.8507 34.0375L82.5607 34.0375L82.9907 33.0175L94.1007 33.0175L94.1007 30.4075L83.8207 30.4075L84.0607 29.3075C88.1846 29.1641 91.9101 28.9946 95.2307 28.7975L95.2307 26.1075C87.5541 26.5376 79.9302 26.7575 72.3607 26.7575L72.3607 29.4675C75.1636 29.4675 77.9557 29.4413 80.7407 29.3875C80.705 29.5488 80.6114 29.8878 80.4507 30.4075L73.4907 30.4075L73.4907 33.0175L79.4307 33.0175C79.2165 33.4835 79.0457 33.8224 78.9207 34.0375L71.7407 34.0375L71.7407 36.6975L77.1307 36.6975C75.6311 38.5613 73.7358 40.1876 71.4507 41.5675L71.4507 45.4075ZM25.9 48.2544C23.7934 49.8135 21.1599 50.6065 18 50.6244L18 47.6444C19.8924 47.5906 21.4846 47.2658 22.77 46.6744L19.29 44.9244L20.14 42.9644L18 42.9644L18 40.0544L21.4 40.0544L22.1 38.4744L25.47 38.4744L24.78 40.0544L31.76 40.0544L31.76 42.9644L29.94 42.9644C29.3866 44.2189 28.7541 45.2883 28.04 46.1844L31.31 47.8844L31.31 50.0044C32.9703 47.6747 34.2617 45.552 35.19 43.6344L33.18 37.7244L31.18 37.7244L31.47 38.2844L28.47 38.2844C27.8095 36.9224 27.3356 35.4777 27.05 33.9544L26.3 33.9544L26.3 38.0744L23.2 38.0744L23.2 33.9544L22.47 33.9544C22.1487 35.5673 21.6748 37.012 21.05 38.2844L18.03 38.2844C18.762 36.9582 19.3151 35.5135 19.69 33.9544L18 33.9544L18 30.8944L23.2 30.8944L23.2 26.3744L26.3 26.3744L26.3 30.8944L31.5 30.8944L31.5 33.9544L29.81 33.9544C30.0956 35.1372 30.478 36.2308 30.96 37.2344C32.0669 33.7577 32.8051 30.0591 33.18 26.1344L36.48 26.1344C36.4264 26.7975 36.3228 27.6833 36.18 28.7944L42.56 28.7944L42.56 32.2944L41.57 32.2944C41.445 33.979 41.2478 35.6336 40.98 37.2644C40.6051 39.3074 40.1191 41.184 39.53 42.9044L42.69 50.3544L39.24 50.3544L37.58 46.3744C37.098 47.3601 36.3354 48.6877 35.3 50.3544L31.31 50.3544L31.31 51.0044L25.9 48.2544ZM50.9894 50.6257L45.5794 50.6257L45.5794 47.3757L47.5094 47.3757L47.5094 42.1257L44.8594 42.8057L44.8594 39.0657L47.5094 38.3957L47.5094 33.4157L45.1294 33.4157L45.1294 30.0357L47.5094 30.0357L47.5094 26.2957L50.9894 26.2957L50.9894 30.0357L53.1294 30.0357L53.1294 33.4157L50.9894 33.4157L50.9894 37.5357L53.3994 36.9157L53.3994 40.6457L50.9894 41.2657L50.9894 50.6257ZM98.5508 32.5357L102.971 32.5357L102.971 50.6257L106.341 50.6257L106.341 39.6857L108.751 41.1857L108.751 37.1257L106.341 35.6257L106.341 32.5357L109.051 32.5357L109.051 29.1957L106.341 29.1957L106.341 26.2957L102.971 26.2957L102.971 29.1957L98.5508 29.1957L98.5508 32.5357ZM57.1362 44.9214C56.8952 46.9465 56.4875 48.8593 55.9162 50.6514L52.1162 50.6514C52.7053 49.0027 53.1395 47.6739 53.4162 46.6614C53.6929 45.6489 53.8791 44.5563 53.9862 43.3914C54.0933 42.2265 54.1562 40.689 54.1562 38.7714L54.1562 26.5914L68.9862 26.5914L68.9862 34.3614L64.9962 34.3614L64.9962 36.3714L69.6062 36.3714L69.6062 39.5514L64.9962 39.5514L64.9962 41.7514L68.9062 41.7514L68.9062 50.6214L57.7162 50.6214L57.7162 41.7514L61.6262 41.7514L61.6262 39.5514L57.5562 39.5514C57.5205 41.1105 57.3772 42.8963 57.1362 44.9214ZM19.6626 30.2451L18.6426 26.6451L21.5626 26.6451L22.6026 30.2451L19.6626 30.2451ZM29.8375 30.2451L26.9175 30.2451L27.9075 26.6451L30.8575 26.6451L29.8375 30.2451ZM57.5791 29.4947L65.5091 29.4947L65.5091 31.4547L57.5791 31.4547L57.5791 29.4947ZM35.6493 32.2903L38.4593 32.2903C38.1915 35.2652 37.7177 37.8418 37.0393 40.0103L35.2993 34.0603L35.6493 32.2903ZM101.018 49.2244C101.5 46.7154 101.83 44.2538 102.008 41.8344C102.187 39.415 102.268 36.681 102.268 33.6344L99.4383 33.6344C99.4383 39.2616 98.9523 44.4574 97.9883 49.2244L101.018 49.2244ZM57.5791 34.3603L61.6191 34.3603L61.6191 36.3803L57.5791 36.3803L57.5791 34.3603ZM118.498 37.3173L115.288 37.3173L117.058 40.8973C117.7 39.4278 118.176 38.2313 118.498 37.3173ZM90.8681 40.2741L78.4081 40.2741C78.1582 40.525 78.0381 40.6541 78.0381 40.6541L78.0381 41.2941L90.8681 41.2941L90.8681 40.2741ZM23.5153 42.9624L26.6753 42.9624C26.2647 43.6972 25.8295 44.3106 25.3653 44.8124L23.1953 43.6924L23.5153 42.9624ZM90.8681 43.6075L78.0381 43.6075L78.0381 44.5475L90.8681 44.5475L90.8681 43.6075ZM61.0069 44.7096L65.6169 44.7096L65.6169 47.6696L61.0069 47.6696L61.0069 44.7096ZM90.8681 46.8602L78.0381 46.8602L78.0381 47.8802L90.8681 47.8802L90.8681 46.8602Z" />
      <path fill-rule="evenodd" fill="rgba(255, 255, 255, 0.6)" d="M112.192 52.2867L116.394 52.2867L116.889 51.5475Q117.147 51.1625 117.399 50.7699L118.246 52.2867L125.835 52.2867L121.141 43.8302Q122.257 41.3684 123.04 39.0921Q123.95 36.446 124.386 34.3489L124.802 32.3433L114.621 32.3433L114.621 31.7079Q119.238 31.5426 122.484 31.0907L123.921 30.8907L123.921 24.1518L122.082 24.3422Q118.77 24.6851 115.434 24.8452Q112.055 25.0074 109.566 25.0334L108.007 25.0497L108.007 24.629L101.304 24.629L101.304 27.529L96.8974 27.529L96.8974 24.3448L95.1375 24.4434Q83.5829 25.0908 72.3607 25.0908L70.694 25.0908L70.694 31.1342L71.824 31.1342L71.824 32.3708L70.6529 32.3708L70.6529 24.9247L52.6561 24.9247L52.6561 24.629L45.8427 24.629L45.8427 28.369L44.2267 28.369L44.2267 27.1277L38.0584 27.1277Q38.1122 26.6286 38.1412 26.2687L38.2868 24.4677L31.665 24.4677L31.6162 24.9784L27.9667 24.9784L27.9667 24.7077L21.5333 24.7077L21.5333 24.9784L16.4381 24.9784L17.6421 29.2277L16.3333 29.2277L16.3333 35.6211L17.4335 35.6211Q17.0608 36.5912 16.5708 37.479L15.2065 39.9511L16.3333 39.9511L16.3333 44.6311L17.6006 44.6311L17.1344 45.706L17.691 45.9858L16.3333 46.0244L16.3333 52.3005L18.0094 52.291Q22.6668 52.2647 26.0203 50.1852L29.6433 52.0268L29.6433 55.2148L31.3111 52.8746L32.9767 53.7212L32.9767 52.0211L36.2267 52.0211L36.7157 51.2339Q37.058 50.6829 37.3614 50.1799L38.1293 52.0211L43.9127 52.0211L43.9127 52.2924L49.76 52.2924L49.7508 52.3181L57.1342 52.3181L57.1438 52.2881L70.5729 52.2881L70.5729 47.726L72.2078 46.8923Q72.5666 46.7093 72.914 46.5214L72.914 52.2142L95.9874 52.2142L95.9874 50.8911L101.304 50.8911L101.304 52.2924L108.007 52.2924L108.007 52.2867L112.192 52.2867ZM113.481 50.62L115.504 50.62C115.838 50.1212 116.161 49.6187 116.473 49.1122C116.812 48.5631 117.138 48.0093 117.454 47.45L118.4 49.1445L119.224 50.62L123.004 50.62L122.079 48.9533L119.274 43.9C120.131 42.072 120.866 40.2884 121.464 38.55C121.821 37.5117 122.116 36.5543 122.353 35.6767C122.513 35.085 122.646 34.5296 122.754 34.01L112.954 34.01L112.954 32.3433L112.954 30.09C116.703 30.0004 119.808 29.7805 122.254 29.44L122.254 26C121.698 26.0575 121.143 26.1112 120.587 26.161C118.894 26.3129 117.201 26.429 115.514 26.51C113.273 26.6175 111.298 26.6821 109.584 26.7L109.584 38.53C109.584 38.9391 109.578 39.3394 109.568 39.7309C109.566 39.781 109.565 39.8309 109.563 39.8806C109.522 41.1739 109.422 42.3695 109.263 43.4675C109.199 43.9073 109.126 44.3315 109.044 44.74C108.805 45.9264 108.461 47.1937 108.007 48.5462C107.962 48.6811 107.916 48.8168 107.869 48.9533C107.681 49.4954 107.476 50.0509 107.254 50.62L111.004 50.62C111.157 50.1766 111.3 49.7392 111.434 49.3079C111.471 49.1892 111.507 49.0711 111.542 48.9533C111.795 48.1118 112.01 47.2936 112.189 46.4991C112.276 46.1104 112.354 45.7274 112.424 45.35C112.552 44.6547 112.656 43.9329 112.737 43.1841C112.847 42.1573 112.913 41.0796 112.934 39.95L114.445 42.8558L115.284 44.47C114.776 45.4043 114.265 46.2929 113.753 47.1367C113.369 47.7672 112.985 48.3726 112.598 48.9533C112.212 49.5335 111.824 50.0889 111.434 50.62L113.481 50.62ZM108.751 41.1857L108.751 37.1257L107.917 36.6069L106.341 35.6257L106.341 32.5357L109.051 32.5357L109.051 29.1957L106.341 29.1957L106.341 26.2957L102.971 26.2957L102.971 27.529L102.971 29.1957L100.217 29.1957L98.5508 29.1957L98.5508 32.5357L102.971 32.5357L102.971 50.6257L106.341 50.6257L106.341 39.6857L107.084 40.1484L107.863 40.6333L108.007 40.723L108.751 41.1857ZM102.268 33.6344L101.304 33.6344L101.105 33.6344L100.602 33.6344L99.4383 33.6344C99.4383 33.8242 99.4377 34.0135 99.4366 34.2024C99.4088 38.966 99.0292 43.4172 98.3028 47.5577C98.2043 48.119 98.0995 48.6745 97.9883 49.2244L101.018 49.2244C101.12 48.6928 101.216 48.1634 101.304 47.636C101.633 45.6742 101.868 43.7412 102.008 41.8344C102.154 39.8648 102.235 37.6866 102.26 35.3011C102.264 34.9396 102.266 34.5734 102.267 34.2024C102.268 34.0143 102.268 33.8249 102.268 33.6344ZM97.77 34.2024L97.5174 34.2024L97.5174 38.3642L95.9874 38.3642L95.9874 50.7105L96.3547 48.894Q97.7228 42.129 97.77 34.2024ZM94.3207 38.3642L94.3207 37.6375L80.5307 37.6375C80.9056 37.0819 81.105 36.7692 81.1407 36.6975L95.8507 36.6975L95.8507 34.0375L94.184 34.0375L84.3694 34.0375L82.5607 34.0375L82.9907 33.0175L94.1007 33.0175L94.1007 30.4075L92.434 30.4075L85.5266 30.4075L83.8207 30.4075L84.0607 29.3075C88.1846 29.1641 91.9101 28.9946 95.2307 28.7975L95.2307 26.1075C94.6749 26.1386 94.1193 26.1687 93.564 26.1976C86.4503 26.5683 79.3821 26.7575 72.3607 26.7575L72.3607 29.4675C72.9167 29.4675 73.4722 29.4665 74.0274 29.4644C75.6911 29.4582 77.3512 29.4426 79.0086 29.4174C79.4895 29.4101 79.9702 29.4019 80.4507 29.393C80.5474 29.3912 80.644 29.3894 80.7407 29.3875C80.705 29.5488 80.6114 29.8878 80.4507 30.4075L75.1574 30.4075L73.4907 30.4075L73.4907 33.0175L79.4307 33.0175C79.2165 33.4835 79.0457 33.8224 78.9207 34.0375L73.4074 34.0375L71.824 34.0375L71.7407 34.0375L71.7407 36.6975L77.1307 36.6975C76.6652 37.276 76.1616 37.8317 75.6201 38.3642C74.4169 39.5472 73.0265 40.6159 71.4507 41.5675L71.4507 45.4075C71.9551 45.1503 72.4427 44.8851 72.914 44.611C72.9821 44.5714 73.0499 44.5316 73.1174 44.4916C73.6239 44.1912 74.1115 43.8803 74.5807 43.5575L74.5807 50.5475L94.3207 50.5475L94.3207 38.3642ZM73.1234 38.3642L71.2729 38.3642L71.2729 39.712Q72.2554 39.072 73.1234 38.3642ZM66.6629 39.5514L69.6062 39.5514L69.6062 36.3714L64.9962 36.3714L64.9962 34.3614L68.9862 34.3614L68.9862 26.5914L54.1562 26.5914L54.1562 38.7714C54.1562 40.0913 54.1264 41.2312 54.0729 42.1934C54.0487 42.6291 54.0196 43.0283 53.9862 43.3914C53.8791 44.5563 53.6929 45.6489 53.4162 46.6614C53.2398 47.307 52.9993 48.0811 52.6955 48.9847C52.6825 49.0235 52.6693 49.0626 52.6561 49.1018C52.4936 49.5825 52.3136 50.099 52.1162 50.6514L55.9162 50.6514C55.9617 50.5087 56.0061 50.3653 56.0495 50.2211C56.5513 48.554 56.9144 46.7853 57.1362 44.9214C57.3502 43.1235 57.4871 41.5142 57.5401 40.0847C57.5468 39.9041 57.5522 39.7263 57.5562 39.5514L61.6262 39.5514L61.6262 40.0847L61.6262 41.7514L57.7162 41.7514L57.7162 50.6214L68.9062 50.6214L68.9062 41.7514L64.9962 41.7514L64.9962 39.5514L66.6629 39.5514ZM52.4603 40.8873L53.3994 40.6457L53.3994 36.9157L52.6561 37.1069L52.4895 37.1498L51.7327 37.3445L50.9894 37.5357L50.9894 33.4157L53.1294 33.4157L53.1294 31.749L53.1294 31.7024L53.1294 30.0357L50.9894 30.0357L50.9894 26.2957L47.5094 26.2957L47.5094 30.0357L45.1294 30.0357L45.1294 33.4157L47.5094 33.4157L47.5094 38.3957L44.8594 39.0657L44.8594 42.8057L45.8427 42.5534L46.5261 42.378L47.5094 42.1257L47.5094 47.3757L45.5794 47.3757L45.5794 50.6257L50.9894 50.6257L50.9894 41.2657L52.4603 40.8873ZM45.8427 45.709L45.8427 44.274L43.1927 44.954L43.1927 37.768L45.8427 37.098L45.8427 35.0824L43.4627 35.0824L43.4627 33.9611L43.0972 33.9611Q42.9158 35.7614 42.6246 37.5345Q42.106 40.3622 41.31 42.8327L43.9127 48.9689L43.9127 45.709L45.8427 45.709ZM39.53 42.9044C40.1191 41.184 40.6051 39.3074 40.98 37.2644C41.2478 35.6336 41.445 33.979 41.57 32.2944L42.56 32.2944L42.56 28.7944L36.18 28.7944C36.2255 28.4405 36.267 28.1094 36.3042 27.8011C36.3329 27.5631 36.359 27.3386 36.3824 27.1277C36.4239 26.7542 36.4567 26.4232 36.48 26.1344L33.18 26.1344C33.08 27.1816 32.9541 28.2127 32.8022 29.2277C32.5263 31.0722 32.1646 32.8638 31.7165 34.603C31.6286 34.9444 31.5373 35.2837 31.4427 35.6211C31.4018 35.767 31.3603 35.9126 31.3181 36.0577C31.2637 36.245 31.2082 36.4317 31.1518 36.6177C31.0891 36.8241 31.0252 37.0296 30.96 37.2344C30.863 37.0325 30.7701 36.8269 30.6811 36.6177C30.603 36.4339 30.5279 36.2472 30.4559 36.0577C30.4013 35.9138 30.3483 35.7682 30.2972 35.6211C30.1851 35.2984 30.0815 34.9679 29.9863 34.6296C29.9239 34.4079 29.8652 34.1828 29.81 33.9544L31.5 33.9544L31.5 30.8944L26.3 30.8944L26.3 26.3744L23.2 26.3744L23.2 30.8944L18 30.8944L18 33.9544L19.69 33.9544C19.5527 34.5254 19.3915 35.081 19.2064 35.6211C19.0904 35.9594 18.965 36.2916 18.8303 36.6177C18.5927 37.1925 18.326 37.7481 18.03 38.2844L21.05 38.2844C21.2227 37.9328 21.3838 37.5681 21.5333 37.1903C21.5832 37.0642 21.6319 36.9367 21.6792 36.8077C22.0033 35.9244 22.2671 34.9729 22.47 33.9544L23.2 33.9544L23.2 38.0744L26.3 38.0744L26.3 33.9544L27.05 33.9544C27.2346 34.9387 27.4977 35.8902 27.839 36.8077C27.8661 36.8804 27.8936 36.9529 27.9216 37.0251C27.9365 37.0635 27.9515 37.1018 27.9667 37.1401C28.1203 37.5278 28.288 37.9092 28.47 38.2844L31.47 38.2844L31.18 37.7244L33.18 37.7244L33.4056 38.3877L33.4156 38.417L33.4267 38.4497L33.9373 39.9511L35.19 43.6344C34.5996 44.854 33.8623 46.1565 32.9767 47.5425C32.7367 47.9181 32.4858 48.2999 32.224 48.6877C32.0732 48.9111 31.9188 49.1365 31.7608 49.3639C31.6137 49.5756 31.4634 49.7891 31.31 50.0044L31.31 47.8844L30.8994 47.671L29.5432 46.9659L28.04 46.1844C28.3762 45.7625 28.6943 45.3022 28.9946 44.8022C29.3321 44.2403 29.6471 43.6283 29.94 42.9644L31.76 42.9644L31.76 40.0544L24.78 40.0544L24.9168 39.7411L25.47 38.4744L22.1 38.4744L21.5388 39.7411L21.4458 39.9511L21.4 40.0544L18 40.0544L18 42.9644L20.14 42.9644L19.4172 44.6311L19.29 44.9244L20.7004 45.6337L22.77 46.6744C22.0913 46.9866 21.3271 47.2246 20.4784 47.3876C19.7196 47.5332 18.8932 47.619 18 47.6444L18 50.6244C18.5729 50.6212 19.1284 50.5924 19.6667 50.5383C22.0974 50.2938 24.1753 49.5309 25.9 48.2544L29.6433 50.1572L31.31 51.0044L31.31 50.3544L31.6404 50.3544L32.9767 50.3544L33.102 50.3544L35.3 50.3544C35.7788 49.5837 36.1993 48.8855 36.5606 48.26C36.9807 47.533 37.3209 46.9043 37.58 46.3744L38.4128 48.3711L39.24 50.3544L42.69 50.3544L41.9831 48.6877L39.53 42.9044ZM37.8374 36.8015C37.6137 37.9548 37.3476 39.0247 37.0393 40.0103L36.1229 36.8767L35.2993 34.0603L35.6493 32.2903L38.4593 32.2903C38.408 32.8605 38.3491 33.4161 38.2826 33.957C38.1599 34.9554 38.0115 35.9037 37.8374 36.8015ZM32.7856 44.6311L30.9931 44.6311Q30.7439 45.1193 30.4757 45.5722L31.8117 46.2668Q32.3375 45.4266 32.7856 44.6311ZM22.1211 28.5784L21.5626 26.6451L18.6426 26.6451L19.1148 28.3118L19.3743 29.2277L19.6626 30.2451L22.6026 30.2451L22.3087 29.2277L22.1211 28.5784ZM28.646 30.2451L29.8375 30.2451L30.1258 29.2277L30.3853 28.3118L30.8575 26.6451L27.9075 26.6451L27.3758 28.5784L27.1973 29.2277L26.9175 30.2451L28.646 30.2451ZM95.7674 30.4352L95.7674 32.3708L96.8841 32.3708L96.8841 30.3689L95.7674 30.4352ZM57.5791 29.788L57.5791 31.4547L65.5091 31.4547L65.5091 29.4947L57.5791 29.4947L57.5791 29.788ZM57.5791 34.7136L57.5791 36.3803L59.2458 36.3803L59.9524 36.3803L61.6191 36.3803L61.6191 34.3603L57.5791 34.3603L57.5791 34.7136ZM117.912 38.8646C118.145 38.2817 118.34 37.7655 118.498 37.3173L115.288 37.3173L116.085 38.9306L116.112 38.984L117.058 40.8973C117.364 40.1972 117.632 39.5591 117.864 38.984C117.88 38.9439 117.896 38.9041 117.912 38.8646ZM90.8681 43.6075L78.0381 43.6075L78.0381 44.5475L90.8681 44.5475L90.8681 43.6075ZM90.8681 41.2941L90.8681 40.2741L78.4081 40.2741C78.1582 40.525 78.0381 40.6541 78.0381 40.6541L78.0381 41.2941L90.8681 41.2941ZM90.8681 46.8602L78.0381 46.8602L78.0381 47.8802L90.8681 47.8802L90.8681 46.8602ZM25.0101 44.6291L25.3653 44.8124C25.4204 44.7529 25.475 44.6918 25.5293 44.6291C25.9324 44.1633 26.3134 43.61 26.6753 42.9624L23.5153 42.9624L23.1953 43.6924L23.5153 43.8576L25.0101 44.6291ZM62.6736 44.7096L61.0069 44.7096L61.0069 47.6696L62.6736 47.6696L63.9502 47.6696L65.6169 47.6696L65.6169 44.7096L62.6736 44.7096Z" />
      <g>
        <text transform="translate(18, 59)" text-3.5>
          <tspan x="0" y="12.72" line-height="0" fill="#8692AE" font-family="PingFang SC Regular; 10.11d9e1; 2015-05-20" letter-spacing="0">
            数据截止统计时间:</tspan>
          <tspan x="99.168" y="12.72" line-height="0" fill="#8692AE" font-family="Roboto-Regular" letter-spacing="0" />
          <tspan x="102.138703125" y="12.72" line-height="0" fill="#8692AE" font-family="PingFang SC Regular; 10.11d9e1; 2015-05-20" letter-spacing="0">
            {{ time }}</tspan>
        </text>
      </g>
    </g>
    <g mask="url(#mask-5_4)">
      <rect x="228" y="-17.5" width="147" height="147" fill="url(#pattern_fill_5_80)" />
    </g>
    <defs>
      <mask id="mask-5_5" style="mask-type:alpha" maskUnits="userSpaceOnUse">
        <rect x="0" y="0" width="375" height="120" fill="#FFFFFF" />
      </mask>
      <linearGradient id="linear_fill_5_7" x1="76.0986328125" y1="36.4266357421875" x2="76.0986328125" y2="140" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#CAE7FC" />
        <stop offset="1" stop-color="#F5F8FA" />
      </linearGradient>
      <mask id="mask-5_7" style="mask-type:alpha" maskUnits="userSpaceOnUse">
        <path d="M0 0L375 0L375 140L0 140L0 0Z" fill="url(#linear_fill_5_7)" />
      </mask>
      <filter id="filter_5_8" x="101" y="-182" width="407" height="407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_8" stdDeviation="45" />
      </filter>
      <filter id="filter_5_9" x="-90" y="-90.1239013671875" width="324" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_9" stdDeviation="45" />
      </filter>
      <filter id="filter_5_10" x="-90" y="-3.1239013671875" width="324" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_10" stdDeviation="45" />
      </filter>
      <filter id="filter_5_11" x="66" y="-90.1239013671875" width="324" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_11" stdDeviation="45" />
      </filter>
      <filter id="filter_5_12" x="177" y="-35.1239013671875" width="324" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_12" stdDeviation="45" />
      </filter>
      <mask id="mask-5_4" style="mask-type:alpha" maskUnits="userSpaceOnUse">
        <rect x="0" y="0" width="375" height="120" fill="#FFFFFF" />
        <path d="M0 0L375 0L375 140L0 140L0 0Z" fill="url(#linear_fill_5_7)" />
      </mask>
      <pattern id="pattern_fill_5_53" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use transform="matrix(0.0019230769230769232, 0, 0, 0.0019230769230769232, 0, 0)" xlink:href="#image0" />
      </pattern>

      <image id="image0" width="520" height="520" />

      <filter id="filter_5_53" x="38.5" y="-30" width="193" height="193" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feGaussianBlur result="gaussian_blur_5_53" stdDeviation="15" />
      </filter>
      <linearGradient id="linear_fill_5_55_0" x1="18" y1="38.5" x2="123" y2="38.5" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#3346DD" />
        <stop offset="1" stop-color="#0AABEB" />
      </linearGradient>
      <pattern id="pattern_fill_5_80" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use transform="matrix(0.0008333333333333334, 0, 0, 0.0008333333333333334, 0, 0)" xlink:href="#image1" />
      </pattern>

      <image id="image1" width="1200" height="1200" :href="image" />

    </defs>
  </svg>
</template>
