import * as echarts from 'echarts'
import { onMounted, ref } from 'vue'

export function useProcurementChart() {
  const procurementAnalysis = ref([
    { label: '公告比选', value: '185项', count: 185 },
    { label: '邀请比选', value: '125项', count: 125 },
    { label: '竞争性磋商', value: '90项', count: 90 },
    { label: '竞争性谈判', value: '70项', count: 70 },
    { label: '询价择优', value: '60项', count: 60 },
    { label: '单一来源', value: '40项', count: 40 },
  ])

  const chartRef = ref<HTMLElement | null>(null)

  onMounted(() => {
    if (chartRef.value) {
      const myChart = echarts.init(chartRef.value)

      const labels = procurementAnalysis.value.map(item => item.label)
      const colors = [
        ['#409EFF', '#66B1FF'], // 公告比选
        ['#67C23A', '#85CE61'], // 邀请比选
        ['#E6A23C', '#EBAD60'], // 竞争性磋商
        ['#909399', '#A6A9AD'], // 竞争性谈判
        ['#409EFF', '#66B1FF'], // 询价择优
        ['#F56C6C', '#F78989'], // 单一来源
      ]

      const seriesData = procurementAnalysis.value.map((item, index) => ({
        value: item.count,
        name: item.label,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: colors[index][0] },
            { offset: 1, color: colors[index][1] },
          ]),
          borderRadius: 4,
        },
      }))

      const option = {
        grid: {
          left: '0',
          right: '2%',
          bottom: '0',
          top: '0',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          show: false,
        },
        yAxis: {
          type: 'category',
          data: labels.reverse(),
          axisLabel: {
            show: true,
            interval: 0,
            formatter(value: string) {
              return `{name|${value}}`
            },
            rich: {
              name: {
                align: 'left',
                padding: [0, 0, 0, 0],
              },
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: '采购方式',
            type: 'bar',
            data: seriesData.reverse(),
            barWidth: '20px',
            showBackground: true,
            backgroundStyle: {
              color: '#f0f2f5',
              borderRadius: 4,
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}项',
              color: '#909399',
            },
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  })

  return {
    chartRef,
  }
}
