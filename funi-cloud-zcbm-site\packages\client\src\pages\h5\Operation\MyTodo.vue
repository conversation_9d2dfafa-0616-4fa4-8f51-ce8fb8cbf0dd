<script setup lang="ts">
import { computed, ref } from 'vue'
import InfoCard from '@/components/h5/InfoCard/index.vue'
import SearchComponent from '@/components/h5/search/SearchComponent.vue'

const searchModel = ref({
  searchText: '',
  groupValue: '',
})

const dataList = ref([
  {
    id: 1,
    title: '待办事项1',
    no: '001',
    description: '这是第一个待办事项的描述。',
    status: '进行中',
    date: '2025-08-15',
  },
  {
    id: 2,
    title: '待办事项2',
    no: '002',
    description: '这是第二个待办事项的描述。',
    status: '待开始',
    date: '2025-08-16',
  },
  {
    id: 3,
    title: '待办事项3',
    no: '003',
    description: '这是第三个待办事项的描述。',
    status: '已完成',
    date: '2025-08-14',
  },
  {
    id: 4,
    title: '待办事项4',
    no: '004',
    description: '这是第四个待办事项的描述。',
    status: '进行中',
    date: '2025-08-17',
  },
  {
    id: 5,
    title: '待办事项5',
    no: '005',
    description: '这是第五个待办事项的描述。',
    status: '待开始',
    date: '2025-08-18',
  },
])

const todoList = computed(() => {
  return dataList.value.map(t => ({
    ...t,
    title: t.title,
    infoList: [
      { name: '编号', value: t.no },
      { name: '描述', value: t.description },
      { name: '状态', value: t.status },
      { name: '创建日期', value: t.date },
      { name: '截止日期', value: t.date },
    ],
    btns: [
      {
        name: '立即处理',
        type: 'primary',
        onClick: () => {},
        style: {
          position: 'absolute',
          bottom: '1em',
          right: '1em',
        },
      },
    ],
  }))
})
</script>

<template>
  <div w-full h-full overflow-hidden flex="~ col justify-between items-center" bg-gray-1>
    <SearchComponent v-model:model-value="searchModel" placeholder="搜索待办标题" />
    <div flex-1 w-full overflow-y-auto p-3>
      <InfoCard
        v-for="item in todoList"
        :key="item.id"
        :data="item"
      />
    </div>
  </div>
</template>
