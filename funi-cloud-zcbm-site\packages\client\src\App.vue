<script setup lang="ts">
import { onMounted } from 'vue'
import { setDefaultTheme } from '../theme/index'
import Banner from '@/components/web/banner/index.vue'
import Sidebar from '@/components/web/sidebar/index.vue'

onMounted(() => {
  setDefaultTheme()
})
</script>

<template>
  <div id="app">
    <Banner />
    <div class="flex w-1200px m-auto pt-40px">
      <Sidebar />
      <div class="flex-1 ml-60px">
        <router-view />
      </div>
    </div>
  </div>
</template>
