#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules/rolldown/bin/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules/rolldown/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules/rolldown/bin/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules/rolldown/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/rolldown@1.0.0-beta.34/node_modules:/mnt/d/project/funi-cloud-zcbm/funi-cloud-zcbm-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../rolldown/bin/cli.mjs" "$@"
else
  exec node  "$basedir/../../../rolldown/bin/cli.mjs" "$@"
fi
