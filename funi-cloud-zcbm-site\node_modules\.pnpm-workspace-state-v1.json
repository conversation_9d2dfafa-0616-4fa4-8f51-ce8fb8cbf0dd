{"lastValidatedTimestamp": 1756795662199, "projects": {"D:\\project\\funi-cloud-zcbm\\funi-cloud-zcbm-site": {"version": "0.0.0-alpha.1"}, "D:\\project\\funi-cloud-zcbm\\funi-cloud-zcbm-site\\packages\\client": {"name": "client", "version": "0.0.0"}, "D:\\project\\funi-cloud-zcbm\\funi-cloud-zcbm-site\\packages\\utils": {"name": "@funi/utils", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {"default": null, "build": {"@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "tsdown": "^0.14.1", "unplugin-vue-router": "^0.15.0", "vite": "npm:rolldown-vite@^7.1.0"}, "deps": {"@funi-lib/utils": "^0.1.4", "@vueuse/core": "^13.6.0", "dayjs": "^1.11.13", "minimatch": "^10.0.3"}, "devtools": {"@antfu/eslint-config": "^5.2.1", "@iconify-json/carbon": "^1.2.13", "@iconify-json/mdi": "^1.2.3", "@types/node": "^24.2.1", "@unocss/preset-icons": "^66.4.2", "@vue/tsconfig": "^0.7.0", "eslint": "^9.33.0", "simple-git-hooks": "^2.13.1", "typescript": "^5.9.2", "unocss": "^66.4.2", "vue-tsc": "^3.0.5"}, "frontend": {"echarts": "^6.0.0", "theme-colors": "^0.1.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": true}