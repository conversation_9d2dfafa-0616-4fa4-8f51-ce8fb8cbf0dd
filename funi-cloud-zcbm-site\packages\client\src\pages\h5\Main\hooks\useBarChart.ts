import type { Ref } from 'vue'
import * as echarts from 'echarts'
import { onMounted, ref, watch } from 'vue'

interface BarChartDataItem {
  name: string
  value: number
}

export function useBarChart(data: Ref<BarChartDataItem[]>) {
  const chartRef = ref<HTMLElement | null>(null)
  let chartInstance: echarts.ECharts | null = null

  const updateChart = () => {
    if (chartInstance) {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: '2%',
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: data.value.map((item: BarChartDataItem) => item.name),
          axisLabel: {
            rotate: 35, // Tilt labels by 35 degrees
            fontSize: 10, // Smaller font size
          },
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: 'Value',
            type: 'bar',
            barWidth: '60%', // Adjust bar width
            data: data.value.map((item: BarChartDataItem) => item.value),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  { offset: 0, color: '#185BFF' }, // Start color
                  { offset: 1, color: '#5D8CFF' }, // End color
                ],
              ),
            },
          },
        ],
      }
      chartInstance.setOption(option)
    }
  }

  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value)
      updateChart()
    }
  }

  onMounted(() => {
    initChart()
    window.addEventListener('resize', () => {
      chartInstance?.resize()
    })
  })

  watch(data, () => {
    updateChart()
  }, { deep: true })

  return {
    chartRef,
  }
}
