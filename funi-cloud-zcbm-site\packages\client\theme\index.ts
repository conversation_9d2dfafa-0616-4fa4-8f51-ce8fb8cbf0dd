import { getColors } from 'theme-colors'
import { defaultTheme } from './defaultColor'

export function setThemeColors(color: string, type = 'primary'): void {
  if (!color) {
    console.warn('No color provided, using default theme colors.')
    return
  }
  const colors = getColors(color)
  const root = document.documentElement
  const elementMap = {
    50: 'light-9',
    100: 'light-8',
    200: 'light-7',
    300: 'light-6',
    400: 'light-3',
    600: 'dark-2',
  }
  function setRootColor(...args: string[]) {
    const [key, value, type] = args
    if (key === '500') {
      root.style.setProperty(`--el-color-${type}`, value)
      root.style.setProperty(`--van-${type}-color`, value)
      return
    }
    const keyName = elementMap[key as unknown as keyof typeof elementMap]
    if (keyName)
      root.style.setProperty(`--el-color-${type}-${keyName}`, value)
  }
  Object.entries(colors).forEach(([key, value]) => {
    root.style.setProperty(`--funi-${type}-${key}`, value)
    setRootColor(key, value, type)
  })
}

export function getUnocssTheme(): Record<string, string> | undefined {
  function colors(): Record<string, string> | undefined {
    if (!defaultTheme) {
      console.warn('No default theme found, using empty object.')
      return {}
    }

    const object: Record<string, any> = {}
    Object.entries(defaultTheme).forEach(([key, value]) => {
      Object.entries(value).forEach(([shade, _]) => {
        object[`${key}-${shade}`] = `var(--funi-${key}-${shade})`
        if (shade === '500') {
          object[`${key}`] = `var(--funi-${key}-${shade})`
        }
      })
    })
    return object
  }
  return {
    ...(colors() ?? {}),
  }
}

export function setDefaultTheme(): void {
  if (!defaultTheme) {
    console.warn('No default theme found, using empty object.')
    return
  }
  Object.entries(defaultTheme).forEach(([key, value]) => {
    const color = value['500' as keyof typeof value]
    if (color) {
      setThemeColors(color, key)
    }
    else {
      const root = document.documentElement
      Object.entries(value).forEach(([shade, colorValue]) => {
        const cssVar = `--funi-${key}-${shade}`
        root.style.setProperty(cssVar, colorValue)
      })
    }
  })
}
