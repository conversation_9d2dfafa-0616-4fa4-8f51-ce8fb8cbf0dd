<!-- 交易信息列表页 -->
<template>
    <div class="trade-list">
      <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">交易信息</h1>
          <p class="page-subtitle">招标采购项目信息查询</p>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="项目名称">
              <el-input 
                v-model="searchForm.projectName" 
                placeholder="请输入项目名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="项目类型">
              <el-select 
                v-model="searchForm.projectType" 
                placeholder="请选择项目类型"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="工程建设" value="construction" />
                <el-option label="政府采购" value="government" />
                <el-option label="产权交易" value="property" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目状态">
              <el-select 
                v-model="searchForm.status" 
                placeholder="请选择状态"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="招标中" value="bidding" />
                <el-option label="评标中" value="evaluating" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <i class="i-mdi-magnify mr-1"></i>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <i class="i-mdi-refresh mr-1"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据统计 -->
        <div class="stats-section">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon bidding">
                <i class="i-mdi-file-document-outline"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.bidding }}</div>
                <div class="stat-label">招标中</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon evaluating">
                <i class="i-mdi-clipboard-check-outline"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.evaluating }}</div>
                <div class="stat-label">评标中</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon completed">
                <i class="i-mdi-check-circle-outline"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon total">
                <i class="i-mdi-chart-line"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总项目</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目列表 -->
        <div class="list-section">
          <el-table 
            v-loading="loading"
            :data="tableData" 
            stripe
            class="trade-table"
          >
            <el-table-column prop="projectName" label="项目名称" min-width="300">
              <template #default="{ row }">
                <router-link 
                  :to="`/trade/detail?id=${row.id}`" 
                  class="project-link"
                >
                  {{ row.projectName }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="projectType" label="项目类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.projectType)">
                  {{ getTypeText(row.projectType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="budget" label="预算金额" width="150">
              <template #default="{ row }">
                <span class="budget-amount">{{ formatBudget(row.budget) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="publishDate" label="发布时间" width="120" />
            <el-table-column prop="deadline" label="截止时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <router-link :to="`/trade/detail?id=${row.id}`">
                  <el-button type="primary" size="small">
                    查看详情
                  </el-button>
                </router-link>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 搜索表单
const searchForm = ref({
  projectName: '',
  projectType: '',
  status: ''
})

// 统计数据
const stats = ref({
  bidding: 25,
  evaluating: 12,
  completed: 156,
  total: 193
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 模拟数据
const mockData = [
  {
    id: 1,
    projectName: '某某市政道路建设工程招标',
    projectType: 'construction',
    budget: 5000000,
    publishDate: '2024-01-15',
    deadline: '2024-02-15',
    status: 'bidding'
  },
  {
    id: 2,
    projectName: '政府办公设备采购项目',
    projectType: 'government',
    budget: 800000,
    publishDate: '2024-01-14',
    deadline: '2024-02-14',
    status: 'evaluating'
  },
  {
    id: 3,
    projectName: '国有资产处置项目',
    projectType: 'property',
    budget: 2000000,
    publishDate: '2024-01-13',
    deadline: '2024-02-13',
    status: 'completed'
  }
]

// 获取项目类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    construction: '工程建设',
    government: '政府采购',
    property: '产权交易'
  }
  return typeMap[type] || type
}

// 获取项目类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap = {
    construction: 'warning',
    government: 'success',
    property: 'info'
  }
  return typeMap[type] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    bidding: '招标中',
    evaluating: '评标中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    bidding: 'primary',
    evaluating: 'warning',
    completed: 'success'
  }
  return statusMap[status] || ''
}

// 格式化预算金额
const formatBudget = (budget: number) => {
  if (budget >= 10000) {
    return `${(budget / 10000).toFixed(1)}万元`
  }
  return `${budget}元`
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    projectName: '',
    projectType: '',
    status: ''
  }
  loadData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 这里应该调用实际的API
    // const response = await window.$https.fetch('/api/trade/list', {
    //   ...searchForm.value,
    //   page: pagination.value.currentPage,
    //   pageSize: pagination.value.pageSize
    // })
    
    tableData.value = mockData
    pagination.value.total = mockData.length
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.trade-list {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

// 搜索区域
.search-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-form {
    :deep(.el-form-item) {
      margin-bottom: 15px;
      margin-right: 20px;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #2c3e50;
    }
  }
}

// 统计区域
.stats-section {
  margin-bottom: 20px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .stat-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-3px);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }

      &.bidding {
        background: linear-gradient(135deg, #3498db, #2980b9);
      }

      &.evaluating {
        background: linear-gradient(135deg, #f39c12, #e67e22);
      }

      &.completed {
        background: linear-gradient(135deg, #27ae60, #229954);
      }

      &.total {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
      }
    }

    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: #7f8c8d;
        margin-top: 5px;
      }
    }
  }
}

// 列表区域
.list-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .trade-table {
    :deep(.el-table__header) {
      background-color: #f8f9fa;
    }

    :deep(.el-table th) {
      background-color: #f8f9fa;
      color: #2c3e50;
      font-weight: 600;
    }

    :deep(.el-table td) {
      border-bottom: 1px solid #f0f0f0;
    }

    .project-link {
      color: #2c3e50;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        color: #3498db;
      }
    }

    .budget-amount {
      font-weight: 600;
      color: #27ae60;
    }
  }

  .pagination-wrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li.is-active {
        background-color: #3498db;
        border-color: #3498db;
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #3498db;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .trade-list .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .search-section {
    padding: 20px;

    .search-form {
      :deep(.el-form-item) {
        margin-right: 0;
        width: 100%;
      }

      :deep(.el-input),
      :deep(.el-select) {
        width: 100% !important;
      }
    }
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .list-section {
    padding: 15px;
    overflow-x: auto;
  }
}
</style>
