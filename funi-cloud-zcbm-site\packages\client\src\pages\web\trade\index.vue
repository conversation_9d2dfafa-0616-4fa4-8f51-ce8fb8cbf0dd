<!-- 交易信息列表页 -->
<template>
    <div class="">
        <!-- 顶部Tab导航 -->
        <div class="top-tabs">
          <el-tabs v-model="activeTopTab" class="trade-tabs" @tab-change="handleTabChange">
            <el-tab-pane
              v-for="tab in topTabs"
              :key="tab.value"
              :label="tab.label"
              :name="tab.value"
            >
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="项目类型">
              <el-select
                v-model="searchForm.projectType"
                placeholder="全部"
                style="width: 120px"
              >
                <el-option label="全部" value="" />
                <el-option label="工程建设" value="construction" />
                <el-option label="政府采购" value="government" />
                <el-option label="产权交易" value="property" />
              </el-select>
            </el-form-item>
            <el-form-item label="采购方式">
              <el-select
                v-model="searchForm.procurementMethod"
                placeholder="全部"
                style="width: 120px"
              >
                <el-option label="全部" value="" />
                <el-option label="公开招标" value="open" />
                <el-option label="邀请招标" value="invite" />
                <el-option label="竞争性谈判" value="negotiation" />
              </el-select>
            </el-form-item>
            <el-form-item label="采购方式">
              <el-select
                v-model="searchForm.status"
                placeholder="全部"
                style="width: 120px"
              >
                <el-option label="全部" value="" />
                <el-option label="招标中" value="bidding" />
                <el-option label="评标中" value="evaluating" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
            <el-form-item label="采购单位">
              <el-input
                v-model="searchForm.purchaser"
                placeholder="请输入采购单位"
                style="width: 150px"
              />
            </el-form-item>
          </el-form>

          <el-form :model="searchForm" inline class="search-form-row2">
            <el-form-item label="开标日期">
              <el-date-picker
                v-model="searchForm.startDate"
                type="date"
                placeholder="2023.01.08"
                style="width: 120px"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
              />
              <span class="date-separator">-</span>
              <el-date-picker
                v-model="searchForm.endDate"
                type="date"
                placeholder="2023.01.08"
                style="width: 120px"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="项目名称">
              <el-input
                v-model="searchForm.projectName"
                placeholder="请输入项目名称"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="信息名称">
              <el-input
                v-model="searchForm.infoName"
                placeholder="请输入信息名称"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                搜索
              </el-button>
              <el-button @click="handleReset">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>



        <!-- 项目列表 -->
        <div class="list-section">
          <div
            v-for="item in tableData"
            :key="item.id"
            class="project-item"
            @click="handleViewDetail(item)"
          >
            <div class="project-date">
              <div class="date-number">{{ formatDateNumber(item.publishDate) }}</div>
              <div class="date-month">{{ formatDateMonth(item.publishDate) }}</div>
            </div>

            <div class="project-content">
              <h3 class="project-title">{{ item.projectName }}</h3>
              <p class="project-description">{{ item.description }}</p>

              <div class="project-meta">
                <div class="meta-row">
                  <span class="meta-item">
                    <span class="meta-label">招采单位：</span>
                    <span class="meta-value">{{ item.purchaser }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">信息地址：</span>
                    <span class="meta-value link">{{ item.infoAddress }}</span>
                  </span>
                </div>
                <div class="meta-row">
                  <span class="meta-item">
                    <span class="meta-label">发布时间：</span>
                    <span class="meta-value">{{ item.publishDate }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">开标时间：</span>
                    <span class="meta-value">{{ item.deadline }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

const router = useRouter()

// 顶部Tab数据
const activeTopTab = ref('all')
const topTabs = ref([
  { label: '全部', value: 'all' },
  { label: '交易公告', value: 'trade' },
  { label: '变更公告', value: 'change' },
  { label: '流标或终止', value: 'cancel' },
  { label: '评标结果', value: 'result' },
  { label: '中标结果', value: 'winner' }
])

// 搜索表单
const searchForm = ref({
  projectName: '',
  projectType: '',
  procurementMethod: '',
  status: '',
  purchaser: '',
  startDate: '',
  endDate: '',
  infoName: ''
})



// 表格数据
const tableData = ref<any[]>([])
const loading = ref(false)

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 模拟数据
const mockData = [
  {
    id: 1,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'construction',
    budget: 5000000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'bidding',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  },
  {
    id: 2,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'government',
    budget: 800000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'evaluating',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  },
  {
    id: 3,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'property',
    budget: 2000000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'completed',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  },
  {
    id: 4,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'construction',
    budget: 3000000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'bidding',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  },
  {
    id: 5,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'government',
    budget: 1500000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'evaluating',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  },
  {
    id: 6,
    projectName: '成都港淮新城建设投资有限公司港淮建投2025年临时服务单位选标公告',
    projectType: 'property',
    budget: 4000000,
    publishDate: '2025-07-08',
    deadline: '2025-07-20',
    status: 'completed',
    description: '提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动，提高采购效率和质量为一步一步规范政府采购活动...',
    purchaser: '公开招标',
    infoAddress: '交易公告'
  }
]

// 获取项目类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    construction: '工程建设',
    government: '政府采购',
    property: '产权交易'
  }
  return typeMap[type] || type
}

// 获取项目类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap = {
    construction: 'warning',
    government: 'success',
    property: 'info'
  }
  return typeMap[type] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    bidding: '招标中',
    evaluating: '评标中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    bidding: 'primary',
    evaluating: 'warning',
    completed: 'success'
  }
  return statusMap[status] || ''
}

// 格式化预算金额
const formatBudget = (budget: number) => {
  if (budget >= 10000) {
    return `${(budget / 10000).toFixed(1)}万元`
  }
  return `${budget}元`
}

// 搜索
const handleSearch = () => {
  loadData()
}

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTopTab.value = tabName
  loadData()
}

// 格式化日期数字（日）
const formatDateNumber = (dateStr: string) => {
  const date = new Date(dateStr)
  return String(date.getDate()).padStart(2, '0')
}

// 格式化日期月份
const formatDateMonth = (dateStr: string) => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}-${month}`
}

// 重置
const handleReset = () => {
  searchForm.value = {
    projectName: '',
    projectType: '',
    procurementMethod: '',
    status: '',
    purchaser: '',
    startDate: '',
    endDate: '',
    infoName: ''
  }
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 这里应该调用实际的API
    // const response = await window.$https.fetch('/api/trade/list', {
    //   ...searchForm.value,
    //   page: pagination.value.currentPage,
    //   pageSize: pagination.value.pageSize
    // })
    
    tableData.value = mockData
    pagination.value.total = mockData.length
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleViewDetail = (item: any) => {
  router.push(`/trade/detail?id=${item.id}`)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>

// 顶部Tab导航
.top-tabs {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .trade-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
      background-color: #e8e8e8;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #1890ff;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      font-weight: 500;
      padding: 0 30px;
      height: 50px;
      line-height: 50px;

      &.is-active {
        color: #1890ff;
      }

      &:hover {
        color: #1890ff;
      }
    }

    :deep(.el-tabs__content) {
      display: none;
    }
  }
}

// 搜索区域
.search-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-form {
    margin-bottom: 15px;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }

      :deep(.el-form-item__label) {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .search-form-row2 {
    .el-form-item {
      margin-bottom: 0;
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }

      :deep(.el-form-item__label) {
        font-size: 14px;
        color: #666;
      }
    }

    .date-separator {
      margin: 0 8px;
      color: #999;
    }
  }
}



// 列表区域
.list-section {
  .project-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .project-date {
      width: 80px;
      text-align: center;
      margin-right: 20px;
      flex-shrink: 0;

      .date-number {
        font-size: 36px;
        font-weight: bold;
        color: #fa8c16;
        line-height: 1;
        margin-bottom: 5px;
      }

      .date-month {
        font-size: 12px;
        color: #999;
      }
    }

    .project-content {
      flex: 1;

      .project-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 10px 0;
        line-height: 1.4;
      }

      .project-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin: 0 0 15px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .project-meta {
        .meta-row {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .meta-item {
            margin-right: 40px;
            font-size: 12px;

            .meta-label {
              color: #999;
            }

            .meta-value {
              color: #666;

              &.link {
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li.is-active {
        background-color: #1890ff;
        border-color: #1890ff;
      }

      .btn-next,
      .btn-prev {
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .trade-list .container {
    padding: 15px;
  }

  .top-tabs .trade-tabs {
    :deep(.el-tabs__item) {
      padding: 0 20px;
      font-size: 13px;
    }
  }

  .search-section {
    padding: 15px;

    .search-form,
    .search-form-row2 {
      .el-form-item {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }



  .list-section {
    .project-item {
      padding: 15px;

      .project-date {
        width: 60px;
        margin-right: 15px;

        .date-number {
          font-size: 28px;
        }
      }

      .project-content {
        .project-title {
          font-size: 14px;
        }

        .project-description {
          font-size: 12px;
        }

        .project-meta .meta-row .meta-item {
          margin-right: 20px;
          font-size: 11px;
        }
      }
    }
  }
}
</style>
