<template>
    <div class="flex justify-between items-center p-y-20px">
        <div class="flex gap-8px text-size-28px color-black items-center">
            <img :src="icon" class="w-24px h-24px" />
            {{ title }}
        </div>
        <div class="hover:underline text-size-14px color-warning" v-if="desc">{{ desc }} ></div>
    </div>
</template>

<script setup>

const props =defineProps({
    title:String,
    icon:{},
    desc:String,
})

</script>

<style lang="scss" scoped></style>