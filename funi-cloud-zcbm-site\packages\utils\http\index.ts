import FuniJS from '@funi-lib/utils'
import { minimatch } from 'minimatch'
import { isFormData, isString } from '../tools'

export class Base<PERSON>pi extends FuniJS.Http {
  abortController: any
  encryptList: never[]
  constructor() {
    super({
      baseURL: FuniJS.isProduction() || './',
      timeout: 60000,
    })
    this.abortController = new AbortController()
    this.encryptList = []
  }

  static getInstance(): BaseApi {
    if (!BaseApi._instance) {
      BaseApi._instance = new BaseApi()
    }
    return BaseApi._instance
  }

  async queryEncryptList(): Promise<any[]> {
    if (this.encryptList)
      return Promise.resolve(this.encryptList)
    try {
      const res = await super.fetch('/gateway/resource/acl/queryEncryptList')
      this.encryptList = FuniJS?.CryptoUtil?.gatewayDecrypt(res) || []
    }
    catch {
      this.encryptList = []
    }
    return Promise.resolve(this.encryptList)
  }

  isNeedEncrypt(url: string): boolean {
    if (
      !this.encryptList
      || !this.encryptList.length
      || !url
      || typeof url !== 'string'
    ) {
      return false
    }
    const path = new URL(url, location.origin).pathname
    return this.encryptList.some(i => minimatch(path, i))
  }

  isgatewayMatched(url: string): boolean {
    // 匹配/isgateway/或者isgateway/
    return /^\/?isgateway\//.test(url)
  }

  // 实现request拦截
  interceptorsRequest(config: any): any {
    config.signal = config?.signal || this.abortController.signal
    config.headers.authorization = sessionStorage.getItem('token')
    return config
  }

  // 实现response拦截
  interceptorsResponse(response: any): any {
    if (response instanceof Blob) {
      return { data: response }
    }
    else if (response?.data instanceof Blob) {
      return response
    }
    else if (
      ![0, 200].includes(response.status)
      && response.success !== true
    ) {
      return this.handleError(response)
    }
    if (!response.source) {
      return { data: response }
    }
    if (!!response.dataEncrypt && isString(response.data)) {
      // 加密内容以string形式返回
      const decryptdData
        = FuniJS?.CryptoUtil?.decryptdData(response.data) ?? response.data
      response.data = decryptdData
        ? JSON.parse(decryptdData) || {}
        : response.data
    }
    return response
  }

  handleError(response: any): any {
    const respect = super.handleError(response)
    respect.catch((err: any) => {
      if (
        ['100001', '100002', '100003', '100004', '990001', '990002'].includes(
          err.code,
        )
      ) {
        this.abortController.abort()
      }
      else if ([404].includes(err.status)) {
        console.error(err)
      }
      else if ([502, 503].includes(err.status)) {
        // showNotify({
        //   type: 'danger',
        //   message: `Error(${err.status}): ${err.msg}`,
        // })
      }
      else {
        // showNotify({
        //   type: 'danger',
        //   message: err.msg,
        // })
      }
    })

    return respect
  }

  async post<T>(url: string, param: any, config = { headers: {} }): Promise<T | any> {
    await this.queryEncryptList()
    const isgatewayMatched = this.isgatewayMatched(url)
    const isNeedEncrypt = this.isNeedEncrypt(url)
    if (isNeedEncrypt || isgatewayMatched) {
      const encryptMethod = isgatewayMatched
        ? FuniJS?.CryptoUtil?.isgatewayEncrypt
        : isNeedEncrypt
          ? FuniJS?.CryptoUtil?.gatewayEncrypt
          : p => p
      const postData = isFormData(param) ? param : encryptMethod(param || {})
      const contentType = isFormData(param) ? 'multipart/form-data' : ''

      const headers = Object.assign({}, config.headers, {
        'Content-Type': contentType,
        'X-Encrypted': 1,
      })
      const postConfig = Object.assign({}, config, {
        isgateway: isgatewayMatched,
        headers,
      })

      return super.post(url, postData, postConfig).then((data) => {
        if (isgatewayMatched) {
          return FuniJS?.CryptoUtil?.isgatewayDecrypt(data.data)
        }
        else if (isNeedEncrypt) {
          return isString(data)
            ? FuniJS?.CryptoUtil?.gatewayDecrypt(data)
            : data
        }
      })
    }

    return super.post(url, param, config)
  }

  async fetch<T>(url: string, param: any, headers = {}, config = {}): Promise<T | any> {
    if (this.isgatewayMatched(url)) {
      return super.fetch(url, param, headers, { ...config, isgateway: true })
    }
    await this.queryEncryptList()
    if (this.isNeedEncrypt(url)) {
      const searchParams = new URLSearchParams(url.split('?')[1])
      !!param
      && Object.entries(param).forEach(([key, value]) =>
        searchParams.set(key, value as string),
      )
      const paramString = searchParams.toString()
      return super
        .fetch(
          url.split('?')[0],
          paramString
            ? { data: FuniJS?.CryptoUtil?.gatewayEncrypt(paramString) }
            : {},
          headers,
          config,
        )
        .then(data =>
          isString(data) ? FuniJS?.CryptoUtil?.gatewayDecrypt(data) : data,
        )
    }
    return super.fetch(url, param, headers, config)
  }

  downloadFile<T>(url: string, param: any, config = { headers: {} }): Promise<T | any> {
    if (this.isNeedEncrypt(url)) {
      const postData = isFormData(param)
        ? param
        : FuniJS?.CryptoUtil?.gatewayEncrypt(param || {})
      const contentType = isFormData(param)
        ? 'multipart/form-data'
        : 'text/plain'
      const postConfig = Object.assign({}, config, {
        headers: Object.assign({}, config.headers, {
          'Content-Type': contentType,
        }),
      })
      return super.downloadFile(url, postData, postConfig)
    }
    return super.downloadFile(url, param, config)
  }

  upload2<T>(url: string, formData: FormData, configer = {}): Promise<T | any> {
    return new Promise((resolve, reject) => {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob',
        ...configer,
      }
      this._axios.post(url, formData, config).then(
        (response) => {
          const contentType
            = response && response.headers
              ? response.headers['content-type']
              : ''
          if (contentType.toLowerCase().includes('application/octet-stream')) {
            this.downloadDataHandler(response)
              .then(() => {
                resolve(response)
              })
              .catch((err1) => {
                reject(err1)
              })
          }
          else if (response && response.data) {
            const reader = new FileReader()
            reader.onload = function () {
              const dataUrl = reader.result as string
              const base64 = dataUrl.split(',')[1] // 将 dataUrl 转换为 base64 编码的字符串
              const decodedData = atob(base64) // 解码 base64
              let realResponse = {}
              try {
                realResponse = JSON.parse(decodedData)
              }
              catch (e) {
                reject(e)
              }
              resolve(realResponse)
            }
            reader.readAsDataURL(response?.data)
          }
          else {
            resolve(response)
          }
        },
        (err: any) => {
          reject(err)
        },
      )
    })
  }
  // axiox拦截处理end

  /////////////////////////////////////////////////////////////////////////////////
}

export default BaseApi.getInstance() as BaseApi
