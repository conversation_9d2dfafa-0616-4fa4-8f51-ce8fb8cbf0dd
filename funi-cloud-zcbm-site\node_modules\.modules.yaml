hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@9.2.0':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bufbuild/protobuf@2.7.0':
    '@bufbuild/protobuf': private
  '@clack/core@0.5.0':
    '@clack/core': private
  '@clack/prompts@0.11.0':
    '@clack/prompts': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@element-plus/icons-vue@2.3.2(vue@3.5.20(typescript@5.9.2))':
    '@element-plus/icons-vue': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-plugin-eslint-comments@4.5.0(eslint@9.34.0(jiti@2.5.1))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.34.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.3.2(eslint@9.34.0(jiti@2.5.1))':
    '@eslint/compat': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.2':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.34.0':
    '@eslint/js': private
  '@eslint/markdown@7.2.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.4':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@funi/wasm@1.1.10':
    '@funi/wasm': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@3.0.1':
    '@iconify/utils': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@oxc-project/runtime@0.82.3':
    '@oxc-project/runtime': private
  '@oxc-project/types@0.82.3':
    '@oxc-project/types': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgr/core@0.2.9':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@quansync/fs@0.1.5':
    '@quansync/fs': private
  '@rolldown/binding-win32-x64-msvc@1.0.0-beta.34':
    '@rolldown/binding-win32-x64-msvc': private
  '@rolldown/pluginutils@1.0.0-beta.34':
    '@rolldown/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.50.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@stylistic/eslint-plugin@5.3.1(eslint@9.34.0(jiti@2.5.1))':
    '@stylistic/eslint-plugin': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@typescript-eslint/eslint-plugin@8.41.0(@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2))(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.41.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.41.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.41.0':
    '@typescript-eslint/visitor-keys': private
  '@unocss/astro@66.5.0(rolldown-vite@7.1.5(@types/node@24.3.0)(esbuild@0.25.9)(jiti@2.5.1)(sass-embedded@1.91.0)(sass@1.91.0)(yaml@2.8.1))':
    '@unocss/astro': private
  '@unocss/cli@66.5.0':
    '@unocss/cli': private
  '@unocss/config@66.5.0':
    '@unocss/config': private
  '@unocss/core@66.5.0':
    '@unocss/core': private
  '@unocss/extractor-arbitrary-variants@66.5.0':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/inspector@66.5.0':
    '@unocss/inspector': private
  '@unocss/postcss@66.5.0(postcss@8.5.6)':
    '@unocss/postcss': private
  '@unocss/preset-attributify@66.5.0':
    '@unocss/preset-attributify': private
  '@unocss/preset-mini@66.5.0':
    '@unocss/preset-mini': private
  '@unocss/preset-tagify@66.5.0':
    '@unocss/preset-tagify': private
  '@unocss/preset-typography@66.5.0':
    '@unocss/preset-typography': private
  '@unocss/preset-uno@66.5.0':
    '@unocss/preset-uno': private
  '@unocss/preset-web-fonts@66.5.0':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-wind3@66.5.0':
    '@unocss/preset-wind3': private
  '@unocss/preset-wind4@66.5.0':
    '@unocss/preset-wind4': private
  '@unocss/preset-wind@66.5.0':
    '@unocss/preset-wind': private
  '@unocss/reset@66.5.0':
    '@unocss/reset': private
  '@unocss/rule-utils@66.5.0':
    '@unocss/rule-utils': private
  '@unocss/transformer-attributify-jsx@66.5.0':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-compile-class@66.5.0':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-directives@66.5.0':
    '@unocss/transformer-directives': private
  '@unocss/transformer-variant-group@66.5.0':
    '@unocss/transformer-variant-group': private
  '@unocss/vite@66.5.0(rolldown-vite@7.1.5(@types/node@24.3.0)(esbuild@0.25.9)(jiti@2.5.1)(sass-embedded@1.91.0)(sass@1.91.0)(yaml@2.8.1))':
    '@unocss/vite': private
  '@vitejs/plugin-vue-jsx@5.1.1(vite@7.1.4(@types/node@24.3.0)(jiti@2.5.1)(lightningcss@1.30.1)(yaml@2.8.1))(vue@3.5.20(typescript@5.9.2))':
    '@vitejs/plugin-vue-jsx': private
  '@vitejs/plugin-vue@6.0.1(vite@7.1.4(@types/node@24.3.0)(jiti@2.5.1)(lightningcss@1.30.1)(yaml@2.8.1))(vue@3.5.20(typescript@5.9.2))':
    '@vitejs/plugin-vue': private
  '@vitest/eslint-plugin@1.3.6(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@vitest/eslint-plugin': private
  '@volar/language-core@2.4.23':
    '@volar/language-core': private
  '@volar/source-map@2.4.23':
    '@volar/source-map': private
  '@volar/typescript@2.4.23':
    '@volar/typescript': private
  '@vue-macros/common@3.0.0-beta.16(vue@3.5.20(typescript@5.9.2))':
    '@vue-macros/common': private
  '@vue/babel-helper-vue-transform-on@1.5.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.5.0(@babel/core@7.28.3)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.5.0(@babel/core@7.28.3)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.20':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.20':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.20':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.20':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/language-core@3.0.6(typescript@5.9.2)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.20':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.20':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.20':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.20(vue@3.5.20(typescript@5.9.2))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.20':
    '@vue/shared': private
  '@vueuse/core@13.9.0(vue@3.5.20(typescript@5.9.2))':
    '@vueuse/core': private
  '@vueuse/metadata@13.9.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.9.0(vue@3.5.20(typescript@5.9.2))':
    '@vueuse/shared': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  alien-signals@2.0.7:
    alien-signals: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  argparse@2.0.1:
    argparse: private
  ast-kit@2.1.2:
    ast-kit: private
  ast-walker-scope@0.8.2:
    ast-walker-scope: private
  async-validator@4.2.5:
    async-validator: private
  asynckit@0.4.0:
    asynckit: private
  axios@1.9.0:
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  bignumber.js@9.3.1:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birpc@2.5.0:
    birpc: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.4:
    browserslist: private
  buffer-builder@0.2.0:
    buffer-builder: private
  builtin-modules@5.0.0:
    builtin-modules: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  change-case@5.4.4:
    change-case: private
  character-entities@2.0.2:
    character-entities: private
  chokidar@3.6.0:
    chokidar: private
  chokidar@4.0.3:
    chokidar: private
  ci-info@4.3.0:
    ci-info: private
  clean-regexp@1.0.0:
    clean-regexp: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  colorjs.io@0.5.2:
    colorjs.io: private
  combined-stream@1.0.8:
    combined-stream: private
  comment-parser@1.4.1:
    comment-parser: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  core-js-compat@3.45.1:
    core-js-compat: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-tree@3.1.0:
    css-tree: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.18:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@1.0.3:
    detect-libc: private
  detect-libc@2.0.4:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  diff@8.0.2:
    diff: private
  dts-resolver@2.1.2:
    dts-resolver: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  echarts@6.0.0:
    echarts: private
  electron-to-chromium@1.5.211:
    electron-to-chromium: private
  empathic@2.0.0:
    empathic: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.9:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.6.5(eslint@9.34.0(jiti@2.5.1)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.1:
    eslint-flat-config-utils: private
  eslint-json-compat-utils@0.2.1(eslint@9.34.0(jiti@2.5.1))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-merge-processors: private
  eslint-plugin-antfu@3.1.1(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-antfu: private
  eslint-plugin-command@3.3.1(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-es-x: private
  eslint-plugin-import-lite@0.3.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2):
    eslint-plugin-import-lite: private
  eslint-plugin-jsdoc@52.0.4(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.1(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.21.3(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.15.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2):
    eslint-plugin-perfectionist: private
  eslint-plugin-pnpm@1.1.1(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-pnpm: private
  eslint-plugin-regexp@2.10.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-regexp: private
  eslint-plugin-toml@0.12.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@60.0.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.2.0(@typescript-eslint/eslint-plugin@8.41.0(@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2))(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2))(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@10.4.0(@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2))(eslint@9.34.0(jiti@2.5.1))(vue-eslint-parser@10.2.0(eslint@9.34.0(jiti@2.5.1))):
    eslint-plugin-vue: private
  eslint-plugin-yml@1.18.0(eslint@9.34.0(jiti@2.5.1)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.20)(eslint@9.34.0(jiti@2.5.1)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  exsolve@1.0.7:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  format@0.2.2:
    format: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  github-slugger@2.0.0:
    github-slugger: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@16.3.0:
    globals: private
  globrex@0.1.2:
    globrex: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@6.0.0:
    gzip-size: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  ignore@5.3.2:
    ignore: private
  immutable@5.1.3:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsdoc-type-pratt-parser@4.8.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  keyv@4.5.4:
    keyv: private
  kolorist@1.8.0:
    kolorist: private
  levn@0.4.1:
    levn: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  local-pkg@1.1.2:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string-ast@1.0.2:
    magic-string-ast: private
  magic-string@0.30.18:
    magic-string: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@2.0.1:
    mdast-util-frontmatter: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  memoize-one@6.0.0:
    memoize-one: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@2.0.0:
    micromark-extension-frontmatter: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@10.0.3:
    minimatch: private
  mlly@1.8.0:
    mlly: private
  moment@2.30.1:
    moment: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  nth-check@2.1.1:
    nth-check: private
  ofetch@1.4.1:
    ofetch: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-statements@1.0.11:
    parse-statements: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  pathe@2.0.3:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pkg-types@2.3.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  pnpm-workspace-yaml@1.1.1:
    pnpm-workspace-yaml: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.11:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  readdirp@4.1.2:
    readdirp: private
  refa@0.12.1:
    refa: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regjsparser@0.12.0:
    regjsparser: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  reusify@1.1.0:
    reusify: private
  rolldown-plugin-dts@0.15.10(rolldown@1.0.0-beta.34)(typescript@5.9.2)(vue-tsc@3.0.6(typescript@5.9.2)):
    rolldown-plugin-dts: private
  rolldown@1.0.0-beta.34:
    rolldown: private
  rollup@4.50.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  sass-embedded-win32-x64@1.91.0:
    sass-embedded-win32-x64: private
  sass@1.91.0:
    sass: private
  scslre@0.3.0:
    scslre: private
  scule@1.3.0:
    scule: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  sm-crypto@0.3.13:
    sm-crypto: private
  source-map-js@1.2.1:
    source-map-js: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.22:
    spdx-license-ids: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  supports-color@8.1.1:
    supports-color: private
  sync-child-process@1.0.2:
    sync-child-process: private
  sync-message-port@1.1.3:
    sync-message-port: private
  synckit@0.11.11:
    synckit: private
  tapable@2.2.3:
    tapable: private
  theme-colors@0.1.0:
    theme-colors: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  totalist@3.0.1:
    totalist: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.9.2):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.9.2):
    ts-declaration-location: private
  tslib@2.3.0:
    tslib: private
  type-check@0.4.0:
    type-check: private
  ufo@1.6.1:
    ufo: private
  unconfig@7.3.3:
    unconfig: private
  undici-types@7.10.0:
    undici-types: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unplugin-utils@0.2.5:
    unplugin-utils: private
  unplugin-vue-router@0.15.0(@vue/compiler-sfc@3.5.20)(typescript@5.9.2)(vue-router@4.5.1(vue@3.5.20(typescript@5.9.2)))(vue@3.5.20(typescript@5.9.2)):
    unplugin-vue-router: private
  unplugin@2.3.10:
    unplugin: private
  update-browserslist-db@1.1.3(browserslist@4.25.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  varint@6.0.0:
    varint: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.20(typescript@5.9.2)):
    vue-demi: private
  vue-eslint-parser@10.2.0(eslint@9.34.0(jiti@2.5.1)):
    vue-eslint-parser: private
  vue-flow-layout@0.2.0:
    vue-flow-layout: private
  vue-router@4.5.1(vue@3.5.20(typescript@5.9.2)):
    vue-router: private
  vue-tsc@3.0.6(typescript@5.9.2):
    vue-tsc: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  xe-utils@3.7.8:
    xe-utils: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yallist@3.1.1:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zrender@6.0.0:
    zrender: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@parcel/watcher'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 02 Sep 2025 06:45:40 GMT
publicHoistPattern: []
registries:
  '@funi': http://npm.funi.local/
  '@funi-lib': http://npm.funi.local/
  '@funijs': http://npm.funi.local/
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/core@1.5.0'
  - '@emnapi/runtime@1.5.0'
  - '@emnapi/wasi-threads@1.1.0'
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@napi-rs/wasm-runtime@1.0.3'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rolldown/binding-android-arm64@1.0.0-beta.34'
  - '@rolldown/binding-darwin-arm64@1.0.0-beta.34'
  - '@rolldown/binding-darwin-x64@1.0.0-beta.34'
  - '@rolldown/binding-freebsd-x64@1.0.0-beta.34'
  - '@rolldown/binding-linux-arm-gnueabihf@1.0.0-beta.34'
  - '@rolldown/binding-linux-arm64-gnu@1.0.0-beta.34'
  - '@rolldown/binding-linux-arm64-musl@1.0.0-beta.34'
  - '@rolldown/binding-linux-x64-gnu@1.0.0-beta.34'
  - '@rolldown/binding-linux-x64-musl@1.0.0-beta.34'
  - '@rolldown/binding-openharmony-arm64@1.0.0-beta.34'
  - '@rolldown/binding-wasm32-wasi@1.0.0-beta.34'
  - '@rolldown/binding-win32-arm64-msvc@1.0.0-beta.34'
  - '@rolldown/binding-win32-ia32-msvc@1.0.0-beta.34'
  - '@rollup/rollup-android-arm-eabi@4.50.0'
  - '@rollup/rollup-android-arm64@4.50.0'
  - '@rollup/rollup-darwin-arm64@4.50.0'
  - '@rollup/rollup-darwin-x64@4.50.0'
  - '@rollup/rollup-freebsd-arm64@4.50.0'
  - '@rollup/rollup-freebsd-x64@4.50.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.50.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.50.0'
  - '@rollup/rollup-linux-arm64-gnu@4.50.0'
  - '@rollup/rollup-linux-arm64-musl@4.50.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.50.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-musl@4.50.0'
  - '@rollup/rollup-linux-s390x-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-musl@4.50.0'
  - '@rollup/rollup-openharmony-arm64@4.50.0'
  - '@rollup/rollup-win32-arm64-msvc@4.50.0'
  - '@rollup/rollup-win32-ia32-msvc@4.50.0'
  - '@tybys/wasm-util@0.10.0'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - sass-embedded-all-unknown@1.91.0
  - sass-embedded-android-arm64@1.91.0
  - sass-embedded-android-arm@1.91.0
  - sass-embedded-android-riscv64@1.91.0
  - sass-embedded-android-x64@1.91.0
  - sass-embedded-darwin-arm64@1.91.0
  - sass-embedded-darwin-x64@1.91.0
  - sass-embedded-linux-arm64@1.91.0
  - sass-embedded-linux-arm@1.91.0
  - sass-embedded-linux-musl-arm64@1.91.0
  - sass-embedded-linux-musl-arm@1.91.0
  - sass-embedded-linux-musl-riscv64@1.91.0
  - sass-embedded-linux-musl-x64@1.91.0
  - sass-embedded-linux-riscv64@1.91.0
  - sass-embedded-linux-x64@1.91.0
  - sass-embedded-unknown-all@1.91.0
  - sass-embedded-win32-arm64@1.91.0
  - tslib@2.8.1
storeDir: D:\pnpmnode\.pnpm-store\v10
virtualStoreDir: D:\project\funi-cloud-zcbm\funi-cloud-zcbm-site\node_modules\.pnpm
virtualStoreDirMaxLength: 60
