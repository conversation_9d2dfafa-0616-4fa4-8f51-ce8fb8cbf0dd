import { presetIcons } from '@unocss/preset-icons'
import {
  defineConfig,
  presetAttributify,
  presetTypography,
  presetWind3,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import config from './theme/uno.config'

export default defineConfig({
  ...config,
  presets: [
    presetTypography(),
    presetWind3(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
        carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
      },
    }),
  ],
  rules: [
    ['shadow-top', { 'box-shadow': '0 -4px 6px -1px rgba(0, 0, 0, 0.04), 0 -2px 4px -1px rgba(0, 0, 0, 0.04)' }],
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
})
