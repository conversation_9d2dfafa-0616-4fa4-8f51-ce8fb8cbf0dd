<script setup lang="tsx">
import IconAnnouncementManagement from './icons/icon_announcement_management.vue'
import IconContractPerformance from './icons/icon_contract_performance.vue'
import IconEvaluationResult from './icons/icon_evaluation_result.vue'
import IconProjectManagement from './icons/icon_project_management.vue'
import IconSupplementClarification from './icons/icon_supplement_clarification.vue'
import IconWinningBid from './icons/icon_winning_bid_result.vue'

type IconComponent = typeof IconProjectManagement

interface StatItem {
  icon: IconComponent
  title: string
}

const stats: StatItem[] = [
  {
    icon: IconProjectManagement,
    title: '项目管理',
  },
  {
    icon: IconAnnouncementManagement,
    title: '公告管理',
  },
  {
    icon: IconSupplementClarification,
    title: '补遗答疑',
  },
  {
    icon: IconEvaluationResult,
    title: '评标结果',
  },
  {
    icon: IconWinningBid,
    title: '中标结果',
  },
  {
    icon: IconContractPerformance,
    title: '签约履行',
  },
]
</script>

<template>
  <div class="bg-white rounded-lg p-4 h-full">
    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
      <div
        v-for="(item, index) in stats"
        :key="index"
        class="relative pb-[60%] rounded-lg transition-transform hover:scale-[1.02]"
        :class="[
          index === 0 ? 'bg-[#F0F7FF]' : '',
          index === 1 ? 'bg-[#FFF7E6]' : '',
          index === 2 ? 'bg-[#F5F0FF]' : '',
          index === 3 ? 'bg-[#E6FFFB]' : '',
          index === 4 ? 'bg-[#FFF0F0]' : '',
          index === 5 ? 'bg-[#F0F7FF]' : '',
        ]"
      >
        <div class="absolute inset-0 flex flex-col items-center justify-center p-4">
          <div>
            <component :is="item.icon" class="w-12 h-12" />
          </div>
          <div class="text-base text-[#333333]">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
