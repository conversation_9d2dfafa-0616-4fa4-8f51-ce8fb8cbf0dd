<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { computed, ref, watch } from 'vue'

interface RadioOption {
  label: string
  value: string
}

interface SearchModel {
  searchText: string
  groupValue: string
}

const props = defineProps<{
  modelValue: SearchModel
  placeholder?: string
  groupData?: RadioOption[]
}>()

const emit = defineEmits(['update:modelValue', 'search'])

const model = useVModel(props, 'modelValue', emit, {
  defaultValue: {
    searchText: '',
    groupValue: props.groupData && props.groupData.length > 0 ? props.groupData[0].value : '',
  },
  deep: true,
})

const searchText = computed({
  get: () => model.value.searchText,
  set: (value) => {
    model.value.searchText = value
  },
})

const selectedGroup = computed({
  get: () => model.value.groupValue,
  set: (value) => {
    model.value.groupValue = value
  },
})

watch(searchText, () => {
  handleSearch()
})

watch(selectedGroup, () => {
  handleSearch()
})

function handleSearch() {
  emit('search', model.value)
}

function selectGroup(value: string) {
  selectedGroup.value = value
}

const inputText = ref('')

function clearSearchText() {
  searchText.value = ''
  inputText.value = ''
}

function handleInput(e: Event) {
  inputText.value = (e.target as HTMLInputElement).value
}

const isInputFocused = ref(false)
</script>

<template>
  <div class="search-component" p-3 bg-white w-full shadow-md relative z-10>
    <div class="search-input-container rounded-4px" flex items-center p-2 bg-gray-1 relative h-12>
      <div v-if="!searchText && !inputText" absolute inset-y-0 inset-x-4 text-base text-color-secondry pointer-events-none flex="~ justify-self-start items-center gap-2">
        <div i-carbon-search />
        {{ placeholder || '搜索标题名称、编号...' }}
      </div>
      <input
        v-model="searchText"
        type="text"
        absolute inset-y-0 inset-x-2
        text-4
        text-color-regular
        flex-1 outline-none text-base bg-transparent
        @keyup.enter="handleSearch"
        @input="handleInput"
        @focus="isInputFocused = true"
      >
      <div v-if="searchText" class="i-carbon-close-outline" absolute w-4 h-4 right-2 text-color-regular @click="clearSearchText" />
    </div>

    <div v-if="groupData && groupData.length > 0" class="radio-group" mt-4 flex flex-wrap gap-2>
      <div
        v-for="option in groupData"
        :key="option.value"
        class="radio-option" px-4 py-2 rounded-lg cursor-pointer text-sm
        :class="{
          'bg-primary-500 text-white': selectedGroup === option.value,
          'bg-gray-100 text-gray-700': selectedGroup !== option.value,
        }"
        @click="selectGroup(option.value)"
      >
        {{ option.label }}
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles here if needed, or rely on UnoCSS/TailwindCSS */
</style>
