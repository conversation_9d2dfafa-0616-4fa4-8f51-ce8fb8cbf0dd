<script setup lang="ts">
interface TabItem {
  label: string
  value: string
}

defineProps<{
  tabs: TabItem[]
  modelValue: string
}>()

const emit = defineEmits(['update:modelValue'])

function selectTab(value: string) {
  emit('update:modelValue', value)
}
</script>

<template>
  <div
    w-full
    flex="~"
    overflow-x="auto"
    whitespace="nowrap"
    bg="white"
    p="2"
    class="hide-scrollbar"
  >
    <div
      v-for="tab in tabs"
      :key="tab.value"
      px="4"
      py="2"
      :class="{
        'font-bold text-black relative': modelValue === tab.value,
        'text-gray-500': modelValue !== tab.value,
      }"
      @click="selectTab(tab.value)"
    >
      {{ tab.label }}
      <div
        v-if="modelValue === tab.value"
        absolute="~"
        bottom="0"
        left="1/2"
        class="-translate-x-1/2 w-6 h-1 bg-primary-600 rounded-full"
      />
    </div>
  </div>
</template>

<style scoped>
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera*/
}
</style>
