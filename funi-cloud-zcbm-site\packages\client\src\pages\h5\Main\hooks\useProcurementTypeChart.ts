import * as echarts from 'echarts'
import { onMounted, ref } from 'vue'

export function useProcurementTypeChart() {
  const procurementTypeAnalysis = ref([
    { label: '货物', value: 256, percentage: 45 },
    { label: '施工', value: 170, percentage: 30 },
    { label: '服务', value: 114, percentage: 20 },
    { label: '其他', value: 28, percentage: 5 },
  ])

  const chartRef = ref<HTMLElement | null>(null)

  onMounted(() => {
    if (chartRef.value) {
      const myChart = echarts.init(chartRef.value)

      const totalProjects = procurementTypeAnalysis.value.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: '0', // Adjusted to move closer to the right edge as per image
          top: 'center',
          itemGap: 20,
          icon: 'rect',
          itemWidth: 10,
          itemHeight: 10,
          formatter: (name: string) => {
            const item = procurementTypeAnalysis.value.find(i => i.label === name)
            return `${name} ${item?.value} (${item?.percentage}%)`
          },
          textStyle: {
            fontSize: 14,
            color: '#333',
          },
        },
        series: [
          {
            name: '采购类型',
            type: 'pie',
            radius: ['50%', '80%'],
            center: ['25%', '50%'], // Adjusted to move pie chart to the left as per image
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: () => `{total|${totalProjects}}\n{unit|项目}`,
              rich: {
                total: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#333',
                  align: 'center',
                },
                unit: {
                  fontSize: 14,
                  color: '#999',
                  align: 'center',
                  padding: [5, 0, 0, 0],
                },
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 28,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: procurementTypeAnalysis.value.map(item => ({
              value: item.value,
              name: item.label,
            })),
            color: ['#409EFF', '#E6A23C', '#67C23A', '#909399'], // Confirmed '其他' color to darker grey
          },
        ],
      }

      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  })

  return {
    chartRef,
    procurementTypeAnalysis,
  }
}
