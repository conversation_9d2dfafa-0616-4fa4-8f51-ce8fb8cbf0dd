<template>
  <div>
    <aside class="side-box">
      <nav class="sidebar-nav">
        <div v-for="item in sidebarItems" :key="item.name">
          <router-link :to="item.path" class="nav-item" :class="{ active: $route.path == item.path }">
            <component :is="item.icon"></component>
            <span>{{ item.name }}</span>
          </router-link>
        </div>
      </nav>
    </aside>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import homeSvg from '@/assets/web/sidebar/home.vue'
import tradeSvg from '@/assets/web/sidebar/trade.vue'
import integritySvg from '@/assets/web/sidebar/integrity.vue'
import noticeSvg from '@/assets/web/sidebar/notice.vue'
import policySvg from '@/assets/web/sidebar/policy.vue'
import hotlineSvg from '@/assets/web/sidebar/hotline.vue'
// 侧边栏导航数据
const sidebarItems = ref([
  { name: '首页', path: '/Main/HomeDashboard', icon: homeSvg },
  { name: '交易信息', path: '/trade', icon: tradeSvg },
  { name: '诚信监督', path: '/integrity', icon: integritySvg },
  { name: '通知公告', path: '/notice', icon: noticeSvg },
  { name: '政策法规', path: '/policy', icon: policySvg },
  { name: '咨询热线', path: '/hotline', icon: hotlineSvg }
])
</script>

<style lang="scss" scoped>
.side-box {
  width: 180px;
  background: rgba(247, 247, 247, 1);
  font-size: 20px;

  .sidebar-nav {
    .nav-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      color: #666;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--el-color-primary);
        color: #fff;

        svg {
          color: #fff;
        }
      }

      svg {
        color: var(--el-color-primary);
      }
    }

    .active {
      background-color: var(--el-color-primary);
      color: #fff;

      svg {
        color: #fff;
      }
    }
  }

}
</style>