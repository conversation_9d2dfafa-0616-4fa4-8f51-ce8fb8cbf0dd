<script setup lang="ts">
import { ref } from 'vue'
import DataListComponent from '@/components/h5/table/DataListComponent.vue'
import Tabs from '@/components/h5/tabs/index.vue'
import { useBarChart } from './hooks/useBarChart'
import { useProcurementChart } from './hooks/useProcurementChart'
import { useProcurementTypeChart } from './hooks/useProcurementTypeChart'

const activeTab = ref('thisWeek')

const tabs = [
  { label: '今日', value: 'today' },
  { label: '本周', value: 'thisWeek' },
  { label: '本月', value: 'thisMonth' },
  { label: '本季度', value: 'thisQuarter' },
  { label: '本年', value: 'thisYear' },
  { label: '自定义', value: 'custom' },
]

const dataOverview = ref([
  { title: '总项目数', value: '568', percentage: 12, isIncrease: true },
  { title: '总标段数', value: '342', percentage: 12, isIncrease: false },
  { title: '已完成标段数', value: '285', percentage: 12, isIncrease: true },
  { title: '预算金额(万元)', value: '207,710', percentage: 12, isIncrease: true },
  { title: '采购金额(元)', value: '198,450', percentage: 12, isIncrease: true },
  { title: '中标金额(万元)', value: '189,320', percentage: 12, isIncrease: true },
])

const projectData = ref([
  { name: '项目1', value: 120, count: 10, status: '进行中' },
  { name: '项目2', value: 200, count: 25, status: '已完成' },
  { name: '项目3', value: 150, count: 8, status: '待启动' },
  { name: '项目4', value: 80, count: 15, status: '已取消' },
  { name: '项目5', value: 70, count: 5, status: '进行中' },
  { name: '项目6', value: 110, count: 12, status: '已完成' },
  { name: '项目7', value: 130, count: 7, status: '待启动' },
  { name: '项目8', value: 90, count: 20, status: '进行中' },
  { name: '项目9', value: 180, count: 3, status: '已完成' },
  { name: '项目10', value: 60, count: 18, status: '待启动' },
])

const { chartRef: procurementMethodChartRef } = useProcurementChart()
const { chartRef: procurementTypeChartRef } = useProcurementTypeChart()
const { chartRef: barChartRef } = useBarChart(projectData)

const listHeaders = ref([
  { label: '名称', key: 'name' },
  { label: '数量', key: 'count' },
  { label: '状态', key: 'status' },
  { label: '价值', key: 'value' },
])
</script>

<template>
  <div flex="~ col justify-between items-center" h-full overflow-hidden w-full>
    <Tabs v-model="activeTab" :tabs="tabs" />

    <div w-full flex-1 overflow-y-auto p-4>
      <div grid="~ cols-2 gap-4" bg="white" p="4" rounded="lg">
        <div
          v-for="(item, index) in dataOverview"
          :key="index"
          bg="primary-50"
          p="4"
          rounded="lg"
          shadow="sm"
        >
          <div text="gray-500 sm">
            {{ item.title }}
          </div>
          <div flex="~ items-end justify-between" mt="2">
            <div text="2xl black" font="bold">
              {{ item.value }}
            </div>
            <div
              flex="~ items-center"
              :class="{
                'text-green-500': item.isIncrease,
                'text-red-500': !item.isIncrease,
              }"
            >
              <div
                :class="{
                  'i-carbon-caret-up': item.isIncrease,
                  'i-carbon-caret-down': !item.isIncrease,
                }"
                text="sm"
              />
              <div text="sm">
                {{ item.percentage }}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <div bg="white" p="4" rounded="lg" mt="4">
        <div text="xl black" font="bold" mb="4">
          采购类型分析
          <span text="sm gray-400" ml="2">单位: 件</span>
        </div>
        <div ref="procurementTypeChartRef" h="200px" w-full />
      </div>

      <div bg="white" p="4" rounded="lg" mt="4">
        <div text="xl black" font="bold" mb="4">
          采购方式分析
        </div>
        <div ref="procurementMethodChartRef" h="200px" w-full />
      </div>

      <div bg="white" p="4" rounded="lg" mt="4">
        <div text="xl black" font="bold" mb="4">
          TOP10代理机构交易排行
        </div>
        <div ref="barChartRef" h="400px" w-full />
      </div>

      <DataListComponent :headers="listHeaders" :data="projectData" />
    </div>
  </div>
</template>
