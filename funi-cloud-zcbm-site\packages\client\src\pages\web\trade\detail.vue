<!-- 交易信息详情页 -->
<template>
  <BaseLayout>
    <div class="trade-detail">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <el-button @click="goBack" class="back-btn">
            <i class="i-mdi-arrow-left mr-1"></i>
            返回列表
          </el-button>
          <h1 class="page-title">{{ projectInfo.name }}</h1>
          <div class="project-meta">
            <span class="meta-item">
              <i class="i-mdi-calendar"></i>
              发布时间：{{ projectInfo.publishDate }}
            </span>
            <span class="meta-item">
              <i class="i-mdi-clock-outline"></i>
              截止时间：{{ projectInfo.deadline }}
            </span>
            <el-tag :type="getStatusTagType(projectInfo.status)" size="large">
              {{ getStatusText(projectInfo.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="detail-content">
          <!-- 左侧步骤条 -->
          <div class="steps-sidebar">
            <div class="steps-container">
              <h3 class="steps-title">项目进度</h3>
              <div class="steps-list">
                <div 
                  v-for="(step, index) in projectSteps" 
                  :key="step.id"
                  class="step-item"
                  :class="{ 
                    'active': currentStep === step.id,
                    'completed': step.status === 'completed',
                    'processing': step.status === 'processing',
                    'pending': step.status === 'pending'
                  }"
                  @click="handleStepClick(step.id)"
                >
                  <div class="step-icon">
                    <i v-if="step.status === 'completed'" class="i-mdi-check"></i>
                    <i v-else-if="step.status === 'processing'" class="i-mdi-clock-outline"></i>
                    <span v-else>{{ index + 1 }}</span>
                  </div>
                  <div class="step-content">
                    <div class="step-title">{{ step.title }}</div>
                    <div class="step-date">{{ step.date }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="content-main">
            <div class="content-card">
              <h3 class="content-title">{{ getCurrentStepTitle() }}</h3>
              <div class="content-body" v-html="getCurrentStepContent()"></div>
              
              <!-- 附件下载 -->
              <div v-if="getCurrentStepAttachments().length > 0" class="attachments">
                <h4>相关附件</h4>
                <div class="attachment-list">
                  <div 
                    v-for="attachment in getCurrentStepAttachments()" 
                    :key="attachment.id"
                    class="attachment-item"
                  >
                    <i class="i-mdi-file-document-outline"></i>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <el-button type="primary" size="small" @click="downloadAttachment(attachment)">
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseLayout from '@/components/web/Layout/BaseLayout.vue'

const route = useRoute()
const router = useRouter()

// 当前选中的步骤
const currentStep = ref(1)

// 项目基本信息
const projectInfo = ref({
  id: '',
  name: '',
  publishDate: '',
  deadline: '',
  status: 'bidding'
})

// 项目步骤
const projectSteps = ref([
  {
    id: 1,
    title: '招标公告',
    date: '2024-01-15',
    status: 'completed'
  },
  {
    id: 2,
    title: '报名投标',
    date: '2024-01-20',
    status: 'processing'
  },
  {
    id: 3,
    title: '开标评标',
    date: '2024-02-15',
    status: 'pending'
  },
  {
    id: 4,
    title: '中标公示',
    date: '2024-02-20',
    status: 'pending'
  },
  {
    id: 5,
    title: '合同签订',
    date: '2024-02-25',
    status: 'pending'
  }
])

// 步骤内容
const stepContents = ref({
  1: {
    title: '招标公告',
    content: `
      <h4>项目概况</h4>
      <p>本项目为某某市政道路建设工程，总投资约500万元，建设内容包括道路路面、排水系统、绿化工程等。</p>
      
      <h4>招标范围</h4>
      <ul>
        <li>道路路面工程：包括路基处理、沥青路面铺设等</li>
        <li>排水工程：雨水管道、检查井等设施建设</li>
        <li>绿化工程：道路两侧绿化带建设</li>
        <li>交通设施：标志标线、信号灯等</li>
      </ul>
      
      <h4>投标人资格要求</h4>
      <p>1. 具有独立法人资格的企业</p>
      <p>2. 具有市政公用工程施工总承包二级及以上资质</p>
      <p>3. 近三年内无重大质量安全事故</p>
      <p>4. 具有良好的银行资信和商业信誉</p>
      
      <h4>报名时间及地点</h4>
      <p>报名时间：2024年1月15日至2024年1月25日</p>
      <p>报名地点：某某市公共资源交易中心</p>
    `,
    attachments: [
      { id: 1, name: '招标文件.pdf', url: '/files/tender_doc.pdf' },
      { id: 2, name: '工程量清单.xlsx', url: '/files/quantity_list.xlsx' }
    ]
  },
  2: {
    title: '报名投标',
    content: `
      <h4>投标文件要求</h4>
      <p>投标人应按照招标文件要求编制投标文件，投标文件应包括以下内容：</p>
      
      <h4>商务文件</h4>
      <ul>
        <li>投标函及投标函附录</li>
        <li>法定代表人身份证明</li>
        <li>授权委托书</li>
        <li>投标保证金</li>
        <li>资格审查资料</li>
      </ul>
      
      <h4>技术文件</h4>
      <ul>
        <li>施工组织设计</li>
        <li>项目管理机构</li>
        <li>施工方案</li>
        <li>质量保证措施</li>
        <li>安全文明施工措施</li>
      </ul>
      
      <h4>投标截止时间</h4>
      <p>投标文件递交截止时间：2024年2月15日上午9:00</p>
      <p>逾期送达或者未送达指定地点的投标文件，招标人不予受理。</p>
    `,
    attachments: [
      { id: 3, name: '投标文件格式.doc', url: '/files/bid_format.doc' }
    ]
  },
  3: {
    title: '开标评标',
    content: `
      <h4>开标时间及地点</h4>
      <p>开标时间：2024年2月15日上午9:30</p>
      <p>开标地点：某某市公共资源交易中心开标室</p>
      
      <h4>评标办法</h4>
      <p>本项目采用综合评估法进行评标，评标标准如下：</p>
      
      <h4>评分标准</h4>
      <ul>
        <li>投标报价（40分）：以有效投标的最低投标价为评标基准价</li>
        <li>技术标准（35分）：施工组织设计、施工方案等</li>
        <li>商务标准（15分）：企业资质、业绩、信誉等</li>
        <li>其他因素（10分）：履约能力、服务承诺等</li>
      </ul>
      
      <h4>评标委员会</h4>
      <p>评标委员会由5人组成，其中技术专家3人，经济专家1人，招标人代表1人。</p>
    `,
    attachments: []
  },
  4: {
    title: '中标公示',
    content: `
      <h4>中标候选人公示</h4>
      <p>根据评标委员会评审结果，现将中标候选人情况公示如下：</p>
      
      <h4>第一中标候选人</h4>
      <p>单位名称：某某建设有限公司</p>
      <p>投标报价：4,850,000元</p>
      <p>工期：120日历天</p>
      <p>项目经理：张某某（一级建造师）</p>
      
      <h4>第二中标候选人</h4>
      <p>单位名称：某某工程有限公司</p>
      <p>投标报价：4,920,000元</p>
      <p>工期：125日历天</p>
      <p>项目经理：李某某（一级建造师）</p>
      
      <h4>公示期</h4>
      <p>公示期为3个工作日，自2024年2月20日至2024年2月22日。</p>
      <p>公示期间，对中标候选人有异议的，可以向招标人提出。</p>
    `,
    attachments: [
      { id: 4, name: '中标候选人公示.pdf', url: '/files/winner_notice.pdf' }
    ]
  },
  5: {
    title: '合同签订',
    content: `
      <h4>合同签订通知</h4>
      <p>经公示无异议，确定某某建设有限公司为本项目中标人。</p>
      
      <h4>合同主要条款</h4>
      <ul>
        <li>合同价格：4,850,000元</li>
        <li>工期：120日历天</li>
        <li>质量标准：合格</li>
        <li>付款方式：按工程进度分期支付</li>
      </ul>
      
      <h4>合同签订时间</h4>
      <p>请中标人于2024年2月25日前到招标人处签订合同。</p>
      
      <h4>履约保证金</h4>
      <p>中标人应在合同签订前提交履约保证金，金额为合同价格的5%。</p>
    `,
    attachments: [
      { id: 5, name: '合同模板.doc', url: '/files/contract_template.doc' }
    ]
  }
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    bidding: '招标中',
    evaluating: '评标中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    bidding: 'primary',
    evaluating: 'warning',
    completed: 'success'
  }
  return statusMap[status] || ''
}

// 获取当前步骤标题
const getCurrentStepTitle = () => {
  return stepContents.value[currentStep.value]?.title || ''
}

// 获取当前步骤内容
const getCurrentStepContent = () => {
  return stepContents.value[currentStep.value]?.content || ''
}

// 获取当前步骤附件
const getCurrentStepAttachments = () => {
  return stepContents.value[currentStep.value]?.attachments || []
}

// 步骤点击处理
const handleStepClick = (stepId: number) => {
  currentStep.value = stepId
}

// 下载附件
const downloadAttachment = (attachment: any) => {
  // 这里应该调用实际的下载API
  console.log('下载附件:', attachment.name)
  // window.$https.downloadFile(attachment.url, {})
}

// 返回列表
const goBack = () => {
  router.push('/trade')
}

// 加载项目详情
const loadProjectDetail = async () => {
  try {
    const projectId = route.query.id
    
    // 模拟API调用
    // const response = await window.$https.fetch(`/api/trade/detail/${projectId}`)
    
    // 模拟数据
    projectInfo.value = {
      id: projectId as string,
      name: '某某市政道路建设工程招标',
      publishDate: '2024-01-15',
      deadline: '2024-02-15',
      status: 'bidding'
    }
  } catch (error) {
    console.error('加载项目详情失败:', error)
  }
}

onMounted(() => {
  loadProjectDetail()
})
</script>

<style lang="scss" scoped>
.trade-detail {
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 页面头部
.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .back-btn {
    margin-bottom: 15px;

    :deep(.el-button) {
      border-color: #3498db;
      color: #3498db;

      &:hover {
        background-color: #3498db;
        color: white;
      }
    }
  }

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
    line-height: 1.4;
  }

  .project-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 5px;
      color: #7f8c8d;
      font-size: 14px;

      i {
        font-size: 16px;
      }
    }
  }
}

// 详情内容区域
.detail-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  align-items: start;
}

// 左侧步骤条
.steps-sidebar {
  .steps-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;

    .steps-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 20px 0;
      text-align: center;
    }

    .steps-list {
      .step-item {
        display: flex;
        align-items: flex-start;
        padding: 15px 0;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 15px;

        &:hover {
          background-color: #f8f9fa;
        }

        &.active {
          background-color: #e3f2fd;
          border-left: 4px solid #3498db;
        }

        .step-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 14px;
          font-weight: 600;
          flex-shrink: 0;
        }

        &.completed .step-icon {
          background-color: #27ae60;
          color: white;
        }

        &.processing .step-icon {
          background-color: #f39c12;
          color: white;
        }

        &.pending .step-icon {
          background-color: #bdc3c7;
          color: white;
        }

        .step-content {
          flex: 1;

          .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .step-date {
            font-size: 12px;
            color: #7f8c8d;
          }
        }
      }
    }
  }
}

// 右侧内容区域
.content-main {
  .content-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .content-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 20px 0;
      padding-bottom: 15px;
      border-bottom: 2px solid #f0f0f0;
    }

    .content-body {
      line-height: 1.8;
      color: #2c3e50;

      :deep(h4) {
        color: #3498db;
        font-size: 16px;
        font-weight: 600;
        margin: 20px 0 10px 0;

        &:first-child {
          margin-top: 0;
        }
      }

      :deep(p) {
        margin-bottom: 12px;
        text-align: justify;
      }

      :deep(ul) {
        margin: 10px 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
        }
      }
    }

    .attachments {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      h4 {
        color: #3498db;
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 15px 0;
      }

      .attachment-list {
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 12px 15px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 10px;

          i {
            font-size: 20px;
            color: #3498db;
            margin-right: 10px;
          }

          .attachment-name {
            flex: 1;
            color: #2c3e50;
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .trade-detail .container {
    padding: 0 15px;
  }

  .page-header {
    padding: 20px;

    .page-title {
      font-size: 20px;
    }

    .project-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }

  .detail-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .steps-sidebar .steps-container {
    position: static;
    padding: 20px;
  }

  .content-main .content-card {
    padding: 20px;
  }
}
</style>
