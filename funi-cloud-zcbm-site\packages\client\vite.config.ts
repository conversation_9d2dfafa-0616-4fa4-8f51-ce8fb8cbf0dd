import path from 'node:path'
import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'
import vueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_RUN_MODE } = env
  return {
    plugins: [vueRouter({
      routesFolder: `src/pages/${VITE_RUN_MODE}`,
      exclude: [
        '**/components/**/*',
        '**/_*.vue',
        '**/icon_*.vue',
      ],
    }), vue(), vueJsx(), UnoCSS()],
    resolve: {
      // Vite路径别名配置
      alias: { '@': path.resolve('./src'), '@h5': path.resolve('./src/pages/h5'), '@web': path.resolve('./src/pages/web') },
    },
  }
})
