{"name": "client", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev:h5": "VITE_RUN_MODE=h5 vite", "dev:web": "VITE_RUN_MODE=web vite", "build:h5": "vue-tsc -b && VITE_RUN_MODE=h5 vite build", "build:web": "vue-tsc -b && VITE_RUN_MODE=web vite build"}, "dependencies": {"@funi/utils": "workspace:*", "@vueuse/core": "catalog:deps", "dayjs": "catalog:deps", "echarts": "catalog:frontend", "vue-router": "catalog:frontend"}, "devDependencies": {"@vitejs/plugin-vue": "catalog:build", "@vitejs/plugin-vue-jsx": "catalog:build", "theme-colors": "catalog:frontend", "unplugin-vue-router": "catalog:build", "vue-tsc": "catalog:devtools"}}