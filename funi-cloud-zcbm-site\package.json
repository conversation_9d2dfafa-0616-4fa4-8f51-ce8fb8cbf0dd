{"type": "module", "version": "0.0.0-alpha.1", "private": true, "packageManager": "pnpm@10.14.0", "scripts": {"build:utils": "pnpm -C packages/utils run build", "dev:h5": "pnpm -C packages/client run dev:h5", "dev:web": "pnpm -C packages/client run dev:web"}, "dependencies": {"@funi-lib/utils": "catalog:deps", "element-plus": "^2.11.1"}, "devDependencies": {"@antfu/eslint-config": "catalog:devtools", "@iconify-json/carbon": "catalog:devtools", "@iconify-json/mdi": "catalog:devtools", "@types/node": "catalog:devtools", "@unocss/preset-icons": "catalog:devtools", "@vue/tsconfig": "catalog:devtools", "eslint": "catalog:devtools", "sass-embedded": "^1.91.0", "simple-git-hooks": "catalog:devtools", "tsdown": "catalog:build", "typescript": "catalog:devtools", "unocss": "catalog:devtools", "vite": "catalog:build", "vue": "catalog:frontend"}, "resolutions": {"vite": "catalog:build"}, "simple-git-hooks": {"pre-commit": "pnpm i --frozen-lockfile --ignore-scripts --offline && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}