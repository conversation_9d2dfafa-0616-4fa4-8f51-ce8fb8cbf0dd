<template>
  <div class="base-layout">
    <!-- 顶部Banner区域 -->
    <header class="header-banner">
      <img 
        src="@/assets/web/banner.png" 
        alt="招标采购平台" 
        class="banner-image"
      >
    </header>

    <!-- 内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 底部区域 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-info">
          <div class="footer-section">
            <h3>联系我们</h3>
            <p>地址：某某市某某区某某街道123号</p>
            <p>电话：400-123-4567</p>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="footer-section">
            <h3>服务时间</h3>
            <p>周一至周五：9:00-18:00</p>
            <p>周六至周日：9:00-17:00</p>
            <p>法定节假日休息</p>
          </div>
          <div class="footer-section">
            <h3>友情链接</h3>
            <div class="links">
              <a href="#" target="_blank">政府采购网</a>
              <a href="#" target="_blank">招标投标网</a>
              <a href="#" target="_blank">公共资源交易中心</a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 招标采购管理平台 版权所有</p>
          <p>备案号：某ICP备12345678号</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 基础布局组件，用于所有页面
</script>

<style lang="scss" scoped>
.base-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-banner {
  width: 100%;
  
  .banner-image {
    width: 100%;
    height: auto;
    display: block;
  }
}

.main-content {
  flex: 1;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.footer {
  background-color: #2c3e50;
  color: white;
  padding: 40px 0 20px;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .footer-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
  }
  
  .footer-section {
    h3 {
      color: #3498db;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 600;
    }
    
    p {
      margin-bottom: 8px;
      line-height: 1.6;
      color: #ecf0f1;
    }
    
    .links {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      a {
        color: #ecf0f1;
        text-decoration: none;
        transition: color 0.3s ease;
        
        &:hover {
          color: #3498db;
        }
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 20px;
    text-align: center;
    
    p {
      margin-bottom: 5px;
      color: #bdc3c7;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .footer-info {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-section {
    text-align: center;
  }
}
</style>
