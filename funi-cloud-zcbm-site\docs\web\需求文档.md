# 招标采购平台网站版，主要展示相关信息

# 页面布局
页面布局分为上中下布局：
1. 顶部区域：一张图展示(图片路径为 src/assets/web/banner.png)
2. 内容区域:各个功能模块内容
3. 底部区域:参考图片：![alt text](image.png)

# 功能模块
1. 首页（对应页面路径：/Main/HomeDashboard.vue）
快捷入口：分为`交易信息`、`诚信监督`、`通知公告`、`政策法规`
招标公告：tab切换展示不同的公告类型，每个类型仅展示4个数据
结果公示：同招标公告一样，但是布局不同，为卡片式
友情链接：写死的数据
具体布局参考图片：![alt text](image-1.png)

2. 交易信息（对应页面路径：/trade/index.vue）
* 列表页界面参考图片：![alt text](image-2.png)，接口文档地址：`http://127.0.0.1:4523/export/openapi/2?version=3.0`
* 详情页面路径：/trade/detail.vue,分为左右布局，左侧为步骤条（绿色为已完成，红色为进行中，灰色为未开始），右侧显示为富文本内容。点击不同的步骤条显示不同的富文本内容。界面参考图片：![alt text](image-3.png)

3. 诚信监督
界面参考图片：![alt text](image-4.png)

4. 通知公告
界面参考图片：![alt text](image-5.png)

5. 政策法规
界面参考图片：![alt text](image-6.png)

6. 咨询热线
界面参考图片：![alt text](image-7.png)
