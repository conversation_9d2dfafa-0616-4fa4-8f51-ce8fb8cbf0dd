<script setup lang="ts">
import IconBidResultAnnouncementStageCount from '@h5/Main/icons/icon_bid_result_announcement_stage_count.vue'
import IconContractPerformanceStageCount from '@h5/Main/icons/icon_contract_performance_stage_count.vue'
import IconDashboardBanner from '@h5/Main/icons/icon_dashboard_banner.vue'
import IconEvaluationResultAnnouncementStageCount from '@h5/Main/icons/icon_evaluation_result_announcement_stage_count.vue'
import IconNotice from '@h5/Main/icons/icon_notice.vue'
import IconSupplementClarificationQaCount from '@h5/Main/icons/icon_supplement_clarification_qa_count.vue'
import IconTenderFailureOrTerminationAnnouncementCount from '@h5/Main/icons/icon_tender_failure_or_termination_announcement_count.vue'
import IconTodo from '@h5/Main/icons/icon_todo.vue'
import IconTotalProjectCount from '@h5/Main/icons/icon_total_project_count.vue'
import IconTransactionAnnouncementCount from '@h5/Main/icons/icon_transaction_announcement_count.vue'
import dayjs from 'dayjs'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const noticeList = reactive([
  {
    title: '关于XX项目招标文件澄清公告',
    type: '流标公告',
    date: '2024-01-02 12:30',
  },
  {
    title: '关于XX项目招标文件变更公告',
    type: '变更公告',
    date: '2024-01-02 12:30',
  },
  {
    title: '关于XX项目招标文件延期公告',
    type: '补充公告',
    date: '2024-01-02 12:30',
  },
])

const now = dayjs()

const formattedDate = ref(now.format('YYYY-MM-DD'))

const overviewData = reactive([
  { name: '项目总数量', value: 156, icon: IconTotalProjectCount },
  { name: '交易公告数', value: 89, icon: IconTransactionAnnouncementCount },
  { name: '补遗/澄清/答疑数', value: 23, icon: IconSupplementClarificationQaCount },
  { name: '流标或终止公告数', value: 12, icon: IconTenderFailureOrTerminationAnnouncementCount },
  { name: '评标结果公示阶段数', value: 45, icon: IconEvaluationResultAnnouncementStageCount },
  { name: '中标结果公示阶段数', value: 67, icon: IconBidResultAnnouncementStageCount },
  { name: '签约履行阶段数', value: 34, icon: IconContractPerformanceStageCount },
])

const todoList = reactive([
  {
    title: '办公设备采购项目-标段001需要审核需...',
    type: '公告管理',
    date: '2024-01-02 12:30',
    status: 'urgent',
  },
  {
    title: '办公设备采购项目-标段001需要审核需...',
    type: '项目标段管理',
    date: '2024-01-02 12:30',
    status: 'normal',
  },
  {
    title: '办公设备采购项目-标段001需要审核需...',
    type: '签约履行管理',
    date: '2024-01-02 12:30',
    status: 'urgent',
  },
])
</script>

<template>
  <div w-full min-h="100%" bg-primary-100>
    <div w-full pb="[calc(32%-1em)]" relative>
      <IconDashboardBanner :time="formattedDate" absolute inset-x-0 top-0 bottom="-1em" />
    </div>
    <div relative w-full h-auto rounded-2xl bg-primary-100 p-3>
      <div p-4 bg-white rounded-lg>
        <div text-lg font-bold mb-4>
          数据概览
        </div>
        <div grid grid-cols-2 gap-4>
          <div
            v-for="(item, index) in overviewData"
            :key="index"
            flex flex-col items-start justify-center gap-1 p-3 bg-primary-50 rounded-lg relative
            :class="{ 'col-span-2': item.name === '项目总数量' }"
          >
            <div text-sm text-gray-500>
              {{ item.name }}
            </div>
            <div text-2xl font-bold text-color-primary>
              {{ item.value }}
            </div>
            <div absolute bottom="8px" right="8px" text-6 text-primary-300>
              <component :is="item.icon" />
            </div>
          </div>
        </div>
      </div>
      <div mt-4>
        <!-- 我的待办 -->
        <div p-4 rounded-lg bg="[linear-gradient(180deg,var(--funi-primary-200)_0%,var(--funi-primary-50)_20%,#FFFFFF_100%)]">
          <div flex items-center justify-between mb-4 rounded-lg p-2>
            <div flex items-center gap-2>
              <div text-primary-400 text-6>
                <IconTodo />
              </div>
              <div text-lg font-bold>
                我的待办
              </div>
            </div>
            <div flex items-center text-4 text-color-secondry @click="router.push('/Operation/MyTodo')">
              更多
              <div i-mdi-chevron-right />
            </div>
          </div>
          <div space-y-3>
            <div
              v-for="(item, index) in todoList" :key="index"
              flex flex-col gap-2 p-3 border="b-1 border-light"
              :class="{ 'border-none': index === todoList.length - 1 }"
            >
              <div flex items-center justify-between>
                <div text-4 font-medium line-clamp-1 flex-1>
                  {{ item.title }}
                </div>
                <div
                  shrink-0 px-2 py-0.5 text-xs rounded
                  :class="item.status === 'urgent' ? 'bg-danger-500 text-white' : 'bg-primary-500 text-white'"
                >
                  {{ item.status === 'urgent' ? '紧急' : '普通' }}
                </div>
              </div>
              <div flex items-center justify-between text-xs text-gray-500>
                <div>{{ item.type }}</div>
                <div>{{ item.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 通知公告 -->
      <div mt-4>
        <div p-4 rounded-lg bg="[linear-gradient(180deg,var(--funi-error-200)_0%,var(--funi-error-100)_10%,#FFFFFF_20%)]">
          <div flex items-center justify-between mb-4 rounded-lg p-2>
            <div flex items-center gap-2>
              <div text-primary-400 text-6>
                <IconNotice />
              </div>
              <div text-lg font-bold>
                <span text-error>通知</span>公告
              </div>
            </div>
            <div flex items-center text-4 text-color-secondry @click="router.push('/Operation/AnnouncementManagement')">
              更多
              <div i-mdi-chevron-right />
            </div>
          </div>
          <div space-y-3>
            <div
              v-for="(item, index) in noticeList"
              :key="index"
              flex flex-col gap-2 p-3 border="b-1 border-light"
              :class="{ 'border-none': index === noticeList.length - 1 }"
            >
              <div text-4 font-medium line-clamp-1>
                {{ item.title }}
              </div>
              <div flex items-center justify-between text-xs text-gray-500>
                <div flex items-center gap-2>
                  <div px-2 py-0.5 rounded bg-primary-50 text-primary-500>
                    {{ item.type }}
                  </div>
                </div>
                <div>{{ item.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
