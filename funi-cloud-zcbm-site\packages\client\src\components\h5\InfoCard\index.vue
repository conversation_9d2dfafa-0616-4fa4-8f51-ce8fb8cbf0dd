<script setup lang="ts">
import type { PropType, VNode } from 'vue'
import { isVNode } from 'vue'

interface InfoCardData {
  title: string
  titleTag?: VNode
  infoList: { name: string, value: string | VNode }[]
  btns?: { name: string, type?: string, style?: Record<string, string>, onClick: (data: Omit<InfoCardData, 'btns'>) => void }[]
  [key: string]: any // Allow arbitrary properties
}

defineProps({
  data: {
    type: Object as PropType<InfoCardData>,
    required: true,
  },
})

// Function to get button classes based on type
function getButtonClasses(type?: string) {
  switch (type) {
    case 'primary':
      return 'bg-primary-500 text-white'
    case 'danger':
      return 'bg-danger-500 text-white'
    // Add more types as needed
    default:
      return 'bg-primary-500 text-white' // Default to primary if no type or unknown type
  }
}
</script>

<template>
  <div bg="white" rounded="8px" p="4" shadow="sm" mb="3" relative>
    <div text="5 text-text-primary" font="bold" pb="4" flex="~ justify-between items-center">
      {{ data?.title || '' }}
      <component :is=" data.titleTag" v-if="data.titleTag" />
    </div>
    <div flex="~ col justify-between items-center gap-3">
      <div v-for="(item, index) in data?.infoList || []" :key="index" flex w="full">
        <span w="80px" text="text-secondry 14px">{{ item.name }}</span>
        <div>
          <span v-if="!isVNode(item.value) " flex="1" text="text-primary 14px">{{ item.value }}</span>
          <component :is=" item.value" v-else />
        </div>
      </div>
    </div>
    <div flex="~ justify-end" space-x="8px">
      <button
        v-for="(btn, index) in data?.btns || []"
        :key="index"
        border="none" rounded="4px" px="16px" py="8px" text="14px" cursor="pointer" outline="none" hover="opacity-90"
        :class="getButtonClasses(btn.type)"
        :style="btn.style"
        @click="btn.onClick({ ...data, btns: undefined })"
      >
        {{ btn.name }}
      </button>
    </div>
  </div>
</template>

<style scoped>
/* No scoped styles needed as UnoCSS classes are used */
</style>
