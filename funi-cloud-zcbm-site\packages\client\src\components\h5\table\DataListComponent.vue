<script setup lang="ts">
import { defineProps } from 'vue'

interface Header {
  label: string
  key: string
}

interface DataItem {
  [key: string]: any
}

const props = defineProps<{
  headers: Header[]
  data: DataItem[]
}>()
</script>

<template>
  <div class="data-list-container" bg="white" p="4" rounded="lg" mt="4">
    <div overflow-x-auto>
      <table min-w-full divide-y divide-light>
        <thead bg="primary-300">
          <tr>
            <th
              v-for="header in props.headers"
              :key="header.key"
              scope="col"
              px="6" py="3" text="left xs gray-700 uppercase tracking-wider" font="medium"
            >
              {{ header.label }}
            </th>
          </tr>
        </thead>
        <tbody divide-y divide-dark>
          <tr
            v-for="(row, rowIndex) in props.data"
            :key="rowIndex"
            :class="{ 'bg-white': rowIndex % 2 === 0, 'bg-light': rowIndex % 2 !== 0 }"
          >
            <td
              v-for="header in props.headers"
              :key="header.key"
              px="6" py="3" whitespace="nowrap" text="xs gray-900"
            >
              {{ row[header.key] }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the component here if needed */
</style>
